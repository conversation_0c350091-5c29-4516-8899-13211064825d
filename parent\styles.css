:root {
    --primary-color: #00704A; /* Starbucks green from config */
    --footer-color: #D4E9D7; /* Tea green from config */
    --accent-color: #006241; /* Darker green for accents from config */
    --light-color: #ffffff;
    --dark-color: #333333;
    --gray-color: #f5f5f5;
    --border-color: #e0e0e0;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --sidebar-width: 250px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--gray-color);
    min-height: 100vh;
    display: flex;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--primary-color);
    color: var(--light-color);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    overflow-y: auto;
    transition: all 0.3s;
    z-index: 1000;
}

.sidebar-header {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--light-color);
    text-decoration: none;
}

.sidebar-logo span {
    color: var(--footer-color);
}

.sidebar-user {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.8rem;
}

.user-avatar i {
    font-size: 1.2rem;
}

.user-info h3 {
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.user-info p {
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar-menu {
    padding: 1rem 0;
}

.menu-heading {
    padding: 0.5rem 1.5rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.6;
}

.menu-item {
    padding: 0.8rem 1.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s;
}

.menu-item:hover, .menu-item.active {
    background-color: var(--accent-color);
}

.menu-item i {
    margin-right: 0.8rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.menu-item a {
    color: var(--light-color);
    text-decoration: none;
    font-weight: 500;
    flex: 1;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 2rem;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb span {
    margin: 0 0.5rem;
    color: #999;
}

/* Cards */
.card {
    background-color: var(--light-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--light-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background-color: var(--footer-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.stat-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.3rem;
    color: var(--primary-color);
}

.stat-info p {
    font-size: 0.9rem;
    color: #777;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
}

.btn {
    background: var(--primary-color);
    color: var(--light-color);
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.btn:hover {
    background: var(--accent-color);
}

.btn-secondary {
    background: var(--footer-color);
    color: var(--primary-color);
}

.btn-secondary:hover {
    background: #c3dcc7;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 0.8rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    font-weight: 600;
    color: var(--primary-color);
    background-color: rgba(0, 112, 74, 0.05);
}

tr:last-child td {
    border-bottom: none;
}

.status-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

/* Student Cards */
.student-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.student-card {
    background-color: var(--light-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s;
}

.student-card:hover {
    transform: translateY(-5px);
}

.student-card-header {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 1rem 1.5rem;
    position: relative;
}

.student-card-body {
    padding: 1.5rem;
}

.student-card-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--footer-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: -40px auto 1rem;
    border: 4px solid var(--light-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.student-card-avatar i {
    font-size: 2rem;
    color: var(--primary-color);
}

.student-info {
    text-align: center;
    margin-bottom: 1rem;
}

.student-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
    color: var(--dark-color);
}

.student-info p {
    font-size: 0.9rem;
    color: #777;
    margin-bottom: 0.5rem;
}

.student-details {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-label {
    font-weight: 500;
    color: var(--dark-color);
}

.detail-value {
    color: #777;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
        overflow: visible;
    }
    
    .sidebar-header, .sidebar-user, .menu-heading {
        display: none;
    }
    
    .menu-item span {
        display: none;
    }
    
    .menu-item i {
        margin-right: 0;
    }
    
    .main-content {
        margin-left: 70px;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .student-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-header a {
        margin-top: 0.5rem;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--primary-color, #00704A);
}

.modal-body {
    padding: 1.5rem;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

.btn-cancel {
    background-color: #e0e0e0;
    color: #333;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-cancel:hover {
    background-color: #d0d0d0;
}

.btn-submit {
    background-color: var(--primary-color, #00704A);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-submit:hover {
    background-color: var(--primary-dark, #005a3b);
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.primary-badge {
    background-color: var(--primary-color, #00704A);
    color: white;
}

/* Student Card Header Modifications */
.student-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Form Control Styles */
.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
    border-color: var(--primary-color, #00704A);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 112, 74, 0.2);
}

small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.8rem;
}