/**
 * Universal Confirmation System for School Admin
 * Handles edit and delete confirmations across all pages
 */

// Global variables
let confirmationModal = null;
let editModal = null;
let currentAction = null;
let currentEntityId = null;
let currentEntityType = null;

// Initialize the confirmation system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeConfirmationSystem();
    initializeEditDeleteButtons();
});

/**
 * Initialize the confirmation modal system
 */
function initializeConfirmationSystem() {
    // Create confirmation modal if it doesn't exist
    if (!document.getElementById('universalConfirmationModal')) {
        createConfirmationModal();
    }
    
    // Create edit modal if it doesn't exist
    if (!document.getElementById('universalEditModal')) {
        createEditModal();
    }
}

/**
 * Create the universal confirmation modal
 */
function createConfirmationModal() {
    const modalHTML = `
        <div id="universalConfirmationModal" class="modal" style="display: none;">
            <div class="modal-content" style="max-width: 500px;">
                <div class="modal-header">
                    <h2><i class="fas fa-exclamation-triangle"></i> Confirm Action</h2>
                    <span class="close-modal" onclick="closeUniversalConfirmation()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="universalConfirmationMessage" style="margin-bottom: 1.5rem; font-size: 1.1rem; line-height: 1.5;"></div>
                    <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                        <button type="button" class="btn" onclick="closeUniversalConfirmation()" style="background-color: #6c757d; color: white; padding: 0.75rem 1.5rem;">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="button" id="universalConfirmButton" class="btn btn-danger" style="padding: 0.75rem 1.5rem;">
                            <i class="fas fa-check"></i> Confirm
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

/**
 * Create the universal edit modal
 */
function createEditModal() {
    const modalHTML = `
        <div id="universalEditModal" class="modal" style="display: none;">
            <div class="modal-content" style="max-width: 800px;">
                <div class="modal-header">
                    <h2><i class="fas fa-edit"></i> <span id="editModalTitle">Edit</span></h2>
                    <span class="close-modal" onclick="closeUniversalEdit()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="universalEditFormContainer">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i> Loading...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

/**
 * Initialize edit and delete buttons across all pages
 */
function initializeEditDeleteButtons() {
    // Initialize delete buttons
    const deleteButtons = document.querySelectorAll('a[href*="action=delete"], a[onclick*="delete"], .btn-icon.delete, .btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            handleDeleteClick(this);
        });
    });
    
    // Initialize edit buttons
    const editButtons = document.querySelectorAll('a[href*="edit_"], .btn-icon.edit, .btn-edit');
    editButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            handleEditClick(this);
        });
    });
}

/**
 * Handle delete button clicks
 */
function handleDeleteClick(button) {
    const href = button.getAttribute('href') || '';
    const onclick = button.getAttribute('onclick') || '';
    
    // Extract entity type and ID
    let entityType = 'item';
    let entityId = null;
    
    // Try to extract from href
    if (href) {
        if (href.includes('students.php')) entityType = 'student';
        else if (href.includes('teachers.php')) entityType = 'teacher';
        else if (href.includes('classes.php')) entityType = 'class';
        else if (href.includes('departments.php')) entityType = 'department';
        else if (href.includes('parents.php')) entityType = 'parent';
        else if (href.includes('bursars.php')) entityType = 'bursar';
        else if (href.includes('payments.php')) entityType = 'payment';
        
        const idMatch = href.match(/[?&]id=(\d+)/);
        if (idMatch) entityId = idMatch[1];
    }
    
    // Try to extract from onclick
    if (onclick && !entityId) {
        const idMatch = onclick.match(/\((\d+)\)/);
        if (idMatch) entityId = idMatch[1];
    }
    
    if (entityId) {
        showDeleteConfirmation(entityType, entityId, href, onclick);
    }
}

/**
 * Handle edit button clicks
 */
function handleEditClick(button) {
    const href = button.getAttribute('href') || '';
    const onclick = button.getAttribute('onclick') || '';
    
    // Extract entity type and ID
    let entityType = 'item';
    let entityId = null;
    
    // Try to extract from href
    if (href) {
        if (href.includes('edit_student.php')) entityType = 'student';
        else if (href.includes('edit_teacher.php')) entityType = 'teacher';
        else if (href.includes('edit_class.php')) entityType = 'class';
        else if (href.includes('edit_department.php')) entityType = 'department';
        else if (href.includes('edit_parent.php')) entityType = 'parent';
        else if (href.includes('edit_bursar.php')) entityType = 'bursar';
        else if (href.includes('edit_payment.php')) entityType = 'payment';
        
        const idMatch = href.match(/[?&]id=(\d+)/);
        if (idMatch) entityId = idMatch[1];
    }
    
    // Try to extract from onclick
    if (onclick && !entityId) {
        const idMatch = onclick.match(/\((\d+)\)/);
        if (idMatch) entityId = idMatch[1];
    }
    
    if (entityId) {
        showEditModal(entityType, entityId);
    }
}

/**
 * Show delete confirmation dialog
 */
function showDeleteConfirmation(entityType, entityId, href, onclick) {
    currentAction = 'delete';
    currentEntityType = entityType;
    currentEntityId = entityId;
    
    const modal = document.getElementById('universalConfirmationModal');
    const message = document.getElementById('universalConfirmationMessage');
    const confirmBtn = document.getElementById('universalConfirmButton');
    
    // Set confirmation message
    message.innerHTML = `
        <i class="fas fa-exclamation-triangle" style="color: #f44336; font-size: 2rem; margin-bottom: 1rem;"></i>
        <p style="margin: 0; font-weight: 500;">Are you sure you want to delete this ${entityType}?</p>
        <p style="margin: 0.5rem 0 0 0; color: #666; font-size: 0.9rem;">This action cannot be undone.</p>
    `;
    
    // Set confirm button action
    confirmBtn.onclick = function() {
        executeDelete(entityType, entityId, href, onclick);
    };
    
    // Show modal
    modal.style.display = 'block';
}

/**
 * Show edit modal
 */
function showEditModal(entityType, entityId) {
    currentAction = 'edit';
    currentEntityType = entityType;
    currentEntityId = entityId;
    
    const modal = document.getElementById('universalEditModal');
    const title = document.getElementById('editModalTitle');
    const container = document.getElementById('universalEditFormContainer');
    
    // Set title
    title.textContent = `Edit ${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
    
    // Show loading
    container.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    
    // Show modal
    modal.style.display = 'block';
    
    // Load edit form
    fetch(`get_edit_form.php?type=${entityType}&id=${entityId}`)
        .then(response => response.text())
        .then(html => {
            container.innerHTML = html;
            
            // Add form submit handler
            const form = container.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    showEditConfirmation(form);
                });
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> Error loading form: ${error.message}
                </div>
            `;
        });
}

/**
 * Show edit confirmation
 */
function showEditConfirmation(form) {
    const modal = document.getElementById('universalConfirmationModal');
    const message = document.getElementById('universalConfirmationMessage');
    const confirmBtn = document.getElementById('universalConfirmButton');
    
    // Set confirmation message
    message.innerHTML = `
        <i class="fas fa-edit" style="color: #007bff; font-size: 2rem; margin-bottom: 1rem;"></i>
        <p style="margin: 0; font-weight: 500;">Are you sure you want to update this ${currentEntityType}?</p>
        <p style="margin: 0.5rem 0 0 0; color: #666; font-size: 0.9rem;">The changes will be saved immediately.</p>
    `;
    
    // Change confirm button to primary color for edit
    confirmBtn.className = 'btn btn-primary';
    confirmBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
    
    // Set confirm button action
    confirmBtn.onclick = function() {
        executeEdit(form);
    };
    
    // Show modal
    modal.style.display = 'block';
}

/**
 * Execute delete action
 */
function executeDelete(entityType, entityId, href, onclick) {
    const confirmBtn = document.getElementById('universalConfirmButton');
    const originalContent = confirmBtn.innerHTML;
    
    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
    confirmBtn.disabled = true;
    
    // Determine delete endpoint
    let deleteEndpoint = '';
    if (href && href.includes('action=delete')) {
        // Use existing URL-based delete
        window.location.href = href;
        return;
    } else {
        // Use AJAX delete
        deleteEndpoint = `delete_${entityType}.php`;
    }
    
    // Create form data
    const formData = new FormData();
    formData.append(`${entityType}_id`, entityId);
    
    // Send delete request
    fetch(deleteEndpoint, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeUniversalConfirmation();
            showUniversalAlert('success', `<i class="fas fa-check-circle"></i> ${entityType.charAt(0).toUpperCase() + entityType.slice(1)} deleted successfully!`);
            
            // Remove row from table if exists
            const row = document.getElementById(`${entityType}-row-${entityId}`);
            if (row) {
                row.style.transition = 'opacity 0.3s ease';
                row.style.opacity = '0';
                setTimeout(() => row.remove(), 300);
            } else {
                // Reload page after short delay
                setTimeout(() => window.location.reload(), 1500);
            }
        } else {
            showUniversalAlert('danger', `<i class="fas fa-exclamation-circle"></i> Error: ${data.message}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showUniversalAlert('danger', `<i class="fas fa-exclamation-circle"></i> An error occurred while deleting the ${entityType}.`);
    })
    .finally(() => {
        confirmBtn.innerHTML = originalContent;
        confirmBtn.disabled = false;
        closeUniversalConfirmation();
    });
}

/**
 * Execute edit action
 */
function executeEdit(form) {
    const confirmBtn = document.getElementById('universalConfirmButton');
    const originalContent = confirmBtn.innerHTML;
    
    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    confirmBtn.disabled = true;
    
    // Get form data
    const formData = new FormData(form);
    
    // Determine edit endpoint
    const editEndpoint = `edit_${currentEntityType}.php?id=${currentEntityId}`;
    
    // Send edit request
    fetch(editEndpoint, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(html => {
        closeUniversalConfirmation();
        closeUniversalEdit();
        showUniversalAlert('success', `<i class="fas fa-check-circle"></i> ${currentEntityType.charAt(0).toUpperCase() + currentEntityType.slice(1)} updated successfully!`);
        
        // Reload page after short delay
        setTimeout(() => window.location.reload(), 1500);
    })
    .catch(error => {
        console.error('Error:', error);
        showUniversalAlert('danger', `<i class="fas fa-exclamation-circle"></i> Error updating ${currentEntityType}: ${error.message}`);
    })
    .finally(() => {
        confirmBtn.innerHTML = originalContent;
        confirmBtn.disabled = false;
        confirmBtn.className = 'btn btn-danger'; // Reset to default
    });
}

/**
 * Close confirmation modal
 */
function closeUniversalConfirmation() {
    const modal = document.getElementById('universalConfirmationModal');
    if (modal) {
        modal.style.display = 'none';
    }
    currentAction = null;
    currentEntityId = null;
    currentEntityType = null;
}

/**
 * Close edit modal
 */
function closeUniversalEdit() {
    const modal = document.getElementById('universalEditModal');
    if (modal) {
        modal.style.display = 'none';
    }
    currentAction = null;
    currentEntityId = null;
    currentEntityType = null;
}

/**
 * Show universal alert message
 */
function showUniversalAlert(type, message) {
    // Remove any existing alerts
    const existingAlerts = document.querySelectorAll('.universal-alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} universal-alert`;
    alertDiv.innerHTML = message;
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        alertDiv.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 300);
    }, 5000);
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const confirmationModal = document.getElementById('universalConfirmationModal');
    const editModal = document.getElementById('universalEditModal');
    
    if (event.target === confirmationModal) {
        closeUniversalConfirmation();
    } else if (event.target === editModal) {
        closeUniversalEdit();
    }
});
