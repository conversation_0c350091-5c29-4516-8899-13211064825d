<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Step Student Registration</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: #fff;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #00704a, #008854);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .close-modal {
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.8);
            transition: color 0.3s;
        }

        .close-modal:hover {
            color: white;
        }

        .modal-body {
            padding: 2rem;
        }

        /* Progress Steps */
        .progress-container {
            margin-bottom: 2rem;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e0e0e0;
            color: #999;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            transition: all 0.3s;
        }

        .step.active .step-number {
            background-color: #00704a;
            color: white;
        }

        .step.completed .step-number {
            background-color: #28a745;
            color: white;
        }

        .step-title {
            font-weight: 600;
            color: #333;
            margin-left: 0.5rem;
        }

        .step.active .step-title {
            color: #00704a;
        }

        .step.completed .step-title {
            color: #28a745;
        }

        .step-connector {
            flex: 1;
            height: 2px;
            background-color: #e0e0e0;
            margin: 0 1rem;
            transition: all 0.3s;
        }

        .step.completed + .step .step-connector,
        .step.completed .step-connector {
            background-color: #28a745;
        }

        /* Form Styles */
        .form-step {
            display: none;
        }

        .form-step.active {
            display: block;
            animation: stepFadeIn 0.3s ease-in;
        }

        @keyframes stepFadeIn {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .section-title {
            font-size: 1.3rem;
            color: #00704a;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }

        .required {
            color: #dc3545;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s;
            background-color: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #00704a;
            box-shadow: 0 0 0 3px rgba(0, 112, 74, 0.1);
        }

        .form-control:invalid {
            border-color: #dc3545;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        /* Navigation Buttons */
        .form-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e0e0e0;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background-color: #00704a;
            color: white;
        }

        .btn-primary:hover {
            background-color: #005a3c;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .btn-outline {
            background-color: transparent;
            color: #00704a;
            border: 2px solid #00704a;
        }

        .btn-outline:hover {
            background-color: #00704a;
            color: white;
        }

        .btn:disabled {
            background-color: #e0e0e0;
            color: #999;
            cursor: not-allowed;
            transform: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 1% auto;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .progress-steps {
                flex-direction: column;
                gap: 1rem;
            }

            .step {
                flex-direction: column;
                text-align: center;
            }

            .step-connector {
                display: none;
            }

            .form-navigation {
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* Additional styling for better UX */
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }

        .form-group.error .form-control {
            border-color: #dc3545;
        }

        .form-group.error .error-message {
            display: block;
        }
    </style>
</head>
<body>
    <!-- Multi-Step Add Student Modal -->
    <div id="addStudentMultiStepModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-user-graduate"></i> Add New Student</h2>
                <span class="close-modal" onclick="closeModal('addStudentMultiStepModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Progress Steps -->
                <div class="progress-container">
                    <div class="progress-steps">
                        <div class="step active" id="step-1">
                            <div class="step-number">1</div>
                            <div class="step-title">Student Information</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step" id="step-2">
                            <div class="step-number">2</div>
                            <div class="step-title">Academic Information</div>
                        </div>
                        <div class="step-connector"></div>
                        <div class="step" id="step-3">
                            <div class="step-number">3</div>
                            <div class="step-title">Parent/Guardian Info</div>
                        </div>
                    </div>
                </div>

                <!-- Multi-Step Form -->
                <form action="add_student.php" method="POST" id="multiStepStudentForm">
                    <!-- Step 1: Student Information -->
                    <div class="form-step active" id="form-step-1">
                        <div class="section-title">
                            <i class="fas fa-user"></i> Student Information
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="first_name">First Name <span class="required">*</span></label>
                                <input type="text" id="first_name" name="first_name" class="form-control" placeholder="Enter first name" required>
                                <div class="error-message">Please enter the first name</div>
                            </div>
                            <div class="form-group">
                                <label for="last_name">Last Name <span class="required">*</span></label>
                                <input type="text" id="last_name" name="last_name" class="form-control" placeholder="Enter last name" required>
                                <div class="error-message">Please enter the last name</div>
                            </div>
                            <div class="form-group">
                                <label for="middle_name">Middle Name</label>
                                <input type="text" id="middle_name" name="middle_name" class="form-control" placeholder="Enter middle name (optional)">
                            </div>
                            <div class="form-group">
                                <label for="gender">Gender <span class="required">*</span></label>
                                <select id="gender" name="gender" class="form-control" required>
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                                <div class="error-message">Please select gender</div>
                            </div>
                            <div class="form-group">
                                <label for="dob">Date of Birth <span class="required">*</span></label>
                                <input type="date" id="dob" name="dob" class="form-control" required>
                                <div class="error-message">Please enter date of birth</div>
                            </div>
                            <div class="form-group">
                                <label for="nationality">Nationality</label>
                                <input type="text" id="nationality" name="nationality" class="form-control" placeholder="Enter nationality">
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Academic Information -->
                    <div class="form-step" id="form-step-2">
                        <div class="section-title">
                            <i class="fas fa-graduation-cap"></i> Academic Information
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="admission_number">Admission Number</label>
                                <input type="text" id="admission_number" name="admission_number" class="form-control" placeholder="Enter admission number">
                            </div>
                            <div class="form-group">
                                <label for="class">Class <span class="required">*</span></label>
                                <input type="text" id="class" name="class" class="form-control" placeholder="Enter class" required>
                                <div class="error-message">Please enter the class</div>
                            </div>
                            <div class="form-group">
                                <label for="department_id">Department</label>
                                <select id="department_id" name="department_id" class="form-control">
                                    <option value="">Select Department</option>
                                    <!-- Department options will be populated from PHP -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="academic_year">Academic Year</label>
                                <input type="text" id="academic_year" name="academic_year" class="form-control" placeholder="e.g., 2024/2025">
                            </div>
                            <div class="form-group">
                                <label for="admission_date">Admission Date</label>
                                <input type="date" id="admission_date" name="admission_date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="previous_school">Previous School</label>
                                <input type="text" id="previous_school" name="previous_school" class="form-control" placeholder="Enter previous school name">
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Parent/Guardian Information -->
                    <div class="form-step" id="form-step-3">
                        <div class="section-title">
                            <i class="fas fa-users"></i> Parent/Guardian Information
                        </div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="parent_name">Parent/Guardian Name <span class="required">*</span></label>
                                <input type="text" id="parent_name" name="parent_name" class="form-control" placeholder="Enter parent/guardian name" required>
                                <div class="error-message">Please enter parent/guardian name</div>
                            </div>
                            <div class="form-group">
                                <label for="parent_phone">Parent/Guardian Phone <span class="required">*</span></label>
                                <input type="tel" id="parent_phone" name="parent_phone" class="form-control" placeholder="Enter parent/guardian phone" required>
                                <div class="error-message">Please enter parent/guardian phone</div>
                            </div>
                            <div class="form-group">
                                <label for="parent_email">Parent/Guardian Email</label>
                                <input type="email" id="parent_email" name="parent_email" class="form-control" placeholder="Enter parent/guardian email">
                            </div>
                            <div class="form-group">
                                <label for="parent_occupation">Parent/Guardian Occupation</label>
                                <input type="text" id="parent_occupation" name="parent_occupation" class="form-control" placeholder="Enter occupation">
                            </div>
                            <div class="form-group">
                                <label for="emergency_contact">Emergency Contact</label>
                                <input type="tel" id="emergency_contact" name="emergency_contact" class="form-control" placeholder="Enter emergency contact number">
                            </div>
                            <div class="form-group">
                                <label for="address">Address <span class="required">*</span></label>
                                <textarea id="address" name="address" class="form-control" rows="3" placeholder="Enter complete address" required></textarea>
                                <div class="error-message">Please enter the address</div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="form-navigation">
                        <div>
                            <button type="button" id="prevBtn" class="btn btn-outline" onclick="previousStep()" style="display: none;">
                                <i class="fas fa-arrow-left"></i> Previous
                            </button>
                        </div>
                        <div>
                            <button type="button" id="nextBtn" class="btn btn-primary" onclick="nextStep()">
                                Next <i class="fas fa-arrow-right"></i>
                            </button>
                            <button type="submit" id="submitBtn" class="btn btn-primary" style="display: none;">
                                <i class="fas fa-save"></i> Save Student
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const totalSteps = 3;

        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
            // Reset form to first step when opening
            resetForm();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
            // Reset form when closing
            resetForm();
        }

        function resetForm() {
            currentStep = 1;
            updateStepDisplay();
            document.getElementById('multiStepStudentForm').reset();
            clearValidationErrors();
        }

        function nextStep() {
            if (validateCurrentStep()) {
                if (currentStep < totalSteps) {
                    currentStep++;
                    updateStepDisplay();
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // Update step indicators
            for (let i = 1; i <= totalSteps; i++) {
                const step = document.getElementById(`step-${i}`);
                const formStep = document.getElementById(`form-step-${i}`);
                
                if (i < currentStep) {
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else if (i === currentStep) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    step.classList.remove('active', 'completed');
                }

                // Show/hide form steps
                if (i === currentStep) {
                    formStep.classList.add('active');
                } else {
                    formStep.classList.remove('active');
                }
            }

            // Update navigation buttons
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const submitBtn = document.getElementById('submitBtn');

            prevBtn.style.display = currentStep === 1 ? 'none' : 'flex';
            
            if (currentStep === totalSteps) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'flex';
            } else {
                nextBtn.style.display = 'flex';
                submitBtn.style.display = 'none';
            }
        }

        function validateCurrentStep() {
            const currentFormStep = document.getElementById(`form-step-${currentStep}`);
            const requiredFields = currentFormStep.querySelectorAll('[required]');
            let isValid = true;

            clearValidationErrors();

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    showFieldError(field);
                    isValid = false;
                }
            });

            // Additional validation for specific fields
            if (currentStep === 1) {
                const dob = document.getElementById('dob');
                if (dob.value) {
                    const birthDate = new Date(dob.value);
                    const today = new Date();
                    const age = today.getFullYear() - birthDate.getFullYear();
                    if (age < 3 || age > 25) {
                        showFieldError(dob, 'Please enter a valid birth date (age should be between 3-25 years)');
                        isValid = false;
                    }
                }
            }

            if (currentStep === 3) {
                const email = document.getElementById('parent_email');
                if (email.value && !isValidEmail(email.value)) {
                    showFieldError(email, 'Please enter a valid email address');
                    isValid = false;
                }

                const phone = document.getElementById('parent_phone');
                if (phone.value && !isValidPhone(phone.value)) {
                    showFieldError(phone, 'Please enter a valid phone number');
                    isValid = false;
                }
            }

            return isValid;
        }

        function showFieldError(field, message = null) {
            const formGroup = field.closest('.form-group');
            formGroup.classList.add('error');
            
            if (message) {
                const errorDiv = formGroup.querySelector('.error-message');
                if (errorDiv) {
                    errorDiv.textContent = message;
                }
            }
        }

        function clearValidationErrors() {
            const errorGroups = document.querySelectorAll('.form-group.error');
            errorGroups.forEach(group => {
                group.classList.remove('error');
            });
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function isValidPhone(phone) {
            const phoneRegex = /^[\+]?[0-9\-\(\)\s]{10,}$/;
            return phoneRegex.test(phone);
        }

        // Form submission
        document.getElementById('multiStepStudentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            if (validateCurrentStep()) {
                if (confirm('Are you sure you want to add this student?')) {
                    this.submit();
                }
            }
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('addStudentMultiStepModal');
            if (event.target === modal) {
                closeModal('addStudentMultiStepModal');
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('addStudentMultiStepModal');
                if (modal.style.display === 'block') {
                    closeModal('addStudentMultiStepModal');
                }
            }
        });

        // Initialize the form display
        document.addEventListener('DOMContentLoaded', function() {
            updateStepDisplay();
        });
    </script>
</body>
</html>