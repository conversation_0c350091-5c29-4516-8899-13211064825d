/**
 * Universal Confirmation System Styles
 * CSS for confirmation dialogs and edit modals
 */

/* Modal Base Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease;
    overflow: hidden;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal Header */
.modal-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color, #00704a), var(--accent-color, #00704a));
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: none;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-header h2 i {
    font-size: 1.2rem;
}

.close-modal {
    color: white;
    font-size: 1.8rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

/* Modal Body */
.modal-body {
    padding: 2rem;
}

/* Confirmation Message Styles */
#universalConfirmationMessage,
#confirmationMessage {
    text-align: center;
    margin-bottom: 2rem;
}

#universalConfirmationMessage i,
#confirmationMessage i {
    display: block;
    margin-bottom: 1rem;
}

#universalConfirmationMessage p,
#confirmationMessage p {
    margin: 0.5rem 0;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    font-size: 0.95rem;
    min-width: 120px;
    justify-content: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color, #007bff);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-color, #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #5a6268;
    transform: translateY(-1px);
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 3rem;
    color: var(--primary-color, #007bff);
    font-size: 1.1rem;
}

.loading-spinner i {
    margin-right: 10px;
    font-size: 1.3rem;
}

/* Alert Styles */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid transparent;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alert i {
    font-size: 1.2rem;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Alert Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Form Styles in Modals */
.modal-body .form-group {
    margin-bottom: 1.5rem;
}

.modal-body .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.modal-body .form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.modal-body .form-control:focus {
    border-color: var(--primary-color, #007bff);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.modal-body .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-content {
        margin: 2% auto;
        width: 95%;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-header h2 {
        font-size: 1.1rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        min-width: 100px;
    }
    
    .modal-body .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .modal-content {
        margin: 1% auto;
        width: 98%;
    }
    
    .modal-header {
        padding: 0.8rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .close-modal {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    #universalConfirmationMessage,
    #confirmationMessage {
        margin-bottom: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Smooth transitions for table rows */
.data-table tbody tr {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.data-table tbody tr.removing {
    opacity: 0;
    transform: translateX(-20px);
}

/* Enhanced button hover effects */
.btn-icon {
    transition: all 0.3s ease;
}

.btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Status indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-indicator.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-indicator.danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-indicator.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}
