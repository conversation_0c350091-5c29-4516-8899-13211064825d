:root {
    /* Base Colors */
    --primary-color: #1976d2;
    --primary-dark: #0d47a1;
    --primary-light: #bbdefb;
    --accent-color: #ff6d00;
    --accent-light: #ffab40;
    --success-color: #4caf50;
    --success-light: #e8f5e9;
    --warning-color: #ff9800;
    --warning-light: #fff3cd;
    --danger-color: #f44336;
    --danger-light: #ffebee;
    --light-color: #ffffff;
    --dark-color: #333333;
    --gray-color: #f5f5f5;
    --gray-dark: #757575;
    --gray-light: #f9f9f9;
    --border-color: #e0e0e0;
    
    /* Form Specific Variables */
    --form-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    --input-focus-shadow: 0 0 0 3px rgba(25, 118, 210, 0.25);
    --form-radius: 12px;
    --input-radius: 8px;
    --transition-speed: 0.3s;
    --label-color: #455a64;
    --placeholder-color: #90a4ae;
    --form-bg: #ffffff;
    --form-border: #f0f0f0;
    --form-padding: 2.5rem;
    --form-gap: 1.8rem;
    --input-padding: 0.85rem 1rem;
    --btn-padding: 0.85rem 1.5rem;
    
    /* Modal Specific Variables */
    --modal-overlay: rgba(0, 0, 0, 0.6);
    --modal-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    --modal-header-bg: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --modal-animation-duration: 0.4s;
    --modal-max-height: 85vh;
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-overlay);
    z-index: 1050;
    overflow-y: auto;
    backdrop-filter: blur(5px);
    transition: all var(--transition-speed);
    padding: 20px;
}

.modal-content {
    background-color: var(--light-color);
    margin: 30px auto;
    width: 90%;
    max-width: 800px;
    max-height: var(--modal-max-height);
    border-radius: var(--form-radius);
    box-shadow: var(--modal-shadow);
    animation: modalFadeIn var(--modal-animation-duration) ease-out;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-30px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--modal-header-bg);
    color: white;
    border-radius: var(--form-radius) var(--form-radius) 0 0;
    flex-shrink: 0;
}

.modal-header h2 {
    font-size: 1.5rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-weight: 600;
}

.modal-header h2 i {
    font-size: 1.3rem;
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    background-color: rgba(0, 0, 0, 0.2);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    line-height: 1;
    transition: all var(--transition-speed);
}

.close-modal:hover {
    background-color: var(--danger-color);
    transform: rotate(90deg);
}

.modal-body {
    padding: 2rem;
    overflow-y: auto;
    flex-grow: 1;
}

/* Enhanced Form Styles */
.modal-form .form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--form-border);
    animation: fadeIn 0.5s ease-out;
}

.modal-form .form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.section-title {
    font-size: 1.2rem;
    color: var(--primary-dark);
    margin-bottom: 1.2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    padding-left: 0.5rem;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.section-title i {
    color: var(--primary-color);
    font-size: 1rem;
}

.modal-form .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.2rem;
}

.form-group {
    margin-bottom: 1.2rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.6rem;
    font-weight: 500;
    color: var(--label-color);
    font-size: 0.95rem;
    transition: color var(--transition-speed);
}

.form-group:focus-within label {
    color: var(--primary-color);
}

.required {
    color: var(--danger-color);
    margin-left: 4px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: var(--input-padding);
    border: 2px solid var(--border-color);
    border-radius: var(--input-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: var(--gray-light);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--input-focus-shadow);
    background-color: var(--light-color);
}

.form-control::placeholder {
    color: var(--placeholder-color);
    opacity: 0.7;
}

/* Select Styling */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23455a64' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
    padding-right: 2.5rem;
    cursor: pointer;
}

select.form-control:focus {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231976d2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Textarea Styling */
textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: var(--btn-padding);
    border-radius: var(--input-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed);
    border: none;
    font-size: 1rem;
    text-decoration: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn i {
    font-size: 1.1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #455a64;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Form Validation Styles */
.form-control.is-invalid {
    border-color: var(--danger-color);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
    padding-right: 2.5rem;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.4rem;
    font-size: 0.85rem;
    color: var(--danger-color);
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Form Alerts */
.form-alert {
    padding: 1rem 1.25rem;
    border-radius: var(--input-radius);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    animation: slideDown 0.4s;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-alert i {
    font-size: 1.2rem;
}

.form-alert-success {
    background-color: var(--success-light);
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

.form-alert-danger {
    background-color: var(--danger-light);
    color: #c62828;
    border-left: 4px solid #c62828;
}

.form-alert-warning {
    background-color: var(--warning-light);
    color: #856404;
    border-left: 4px solid #856404;
}

/* Form Help Text */
.form-text {
    margin-top: 0.4rem;
    font-size: 0.85rem;
    color: var(--gray-dark);
}

/* Form Checkbox and Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.form-check-input {
    margin-right: 0.5rem;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.form-check-label {
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .modal-form .form-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
    }

    /* Multi-step form responsive adjustments */
    .form-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .step {
        max-width: none;
        width: 100%;
    }

    .step:not(:last-child)::after {
        display: none;
    }

    .step-title {
        font-size: 0.8rem;
    }

    .step-number {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }
}

/* Input Group */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-append {
    display: flex;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f5f5f5;
    border: 2px solid var(--border-color);
    border-left: none;
    border-top-right-radius: var(--input-radius);
    border-bottom-right-radius: var(--input-radius);
}

/* Floating Labels (Modern Style) */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    height: calc(3.5rem + 2px);
    padding: 1.5rem 1rem 0.5rem;
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity .1s ease-in-out, transform .1s ease-in-out;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: .65;
    transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Multi-Step Form Styles */
.form-steps {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    max-width: 200px;
    opacity: 0.5;
    transition: all var(--transition-speed);
}

.step.active {
    opacity: 1;
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: var(--border-color);
    z-index: 1;
}

.step.active:not(:last-child)::after {
    background-color: var(--primary-color);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--border-color);
    color: var(--gray-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
    transition: all var(--transition-speed);
}

.step.active .step-number {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.step-title {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--gray-dark);
    text-align: center;
    transition: color var(--transition-speed);
}

.step.active .step-title {
    color: var(--primary-color);
    font-weight: 600;
}

/* Form Section Visibility */
.form-section {
    display: none;
    animation: fadeInSlide 0.4s ease-out;
}

.form-section.active {
    display: block;
}

@keyframes fadeInSlide {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Error States for Multi-Step Form */
.form-control.error {
    border-color: var(--danger-color);
    background-color: #fff5f5;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.error-message {
    display: none;
    color: var(--danger-color);
    font-size: 0.85rem;
    margin-top: 0.4rem;
    animation: fadeIn 0.3s;
}

.error-message.show {
    display: block;
}

/* Required Label Styling */
.required-label::after {
    content: ' *';
    color: var(--danger-color);
    font-weight: bold;
}

/* Custom File Input */
.custom-file {
    position: relative;
    display: inline-block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    margin-bottom: 0;
}

.custom-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    margin: 0;
    opacity: 0;
}

.custom-file-label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.custom-file-label::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    display: block;
    height: calc(1.5em + 0.75rem);
    padding: 0.375rem 0.75rem;
    line-height: 1.5;
    color: #495057;
    content: "Browse";
    background-color: #e9ecef;
    border-left: inherit;
    border-radius: 0 0.25rem 0.25rem 0;
}