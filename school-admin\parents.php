<?php
// Start session
session_start();

// Show all PHP errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load config
require_once '../config/config.php';

// Check if school admin is logged in
if (!isset($_SESSION['school_admin_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get school_id from session
$school_id = $_SESSION['school_admin_school_id'] ?? 0;
if (!$school_id) {
    die("Error: School ID not found in session. Please log in again.");
}

// Get database connection
$conn = getDbConnection();

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $parent_id = intval($_GET['id']);

    // Delete the parent
    $stmt = $conn->prepare("DELETE FROM parents WHERE id = ? AND school_id = ?");
    $stmt->bind_param('ii', $parent_id, $school_id);

    if ($stmt->execute()) {
        $_SESSION['parent_success'] = 'Parent deleted successfully!';
    } else {
        $_SESSION['parent_error'] = 'Failed to delete parent: ' . $conn->error;
    }

    $stmt->close();
    header('Location: parents.php');
    exit;
}

// Handle reply to feedback
if (isset($_POST['reply_feedback']) && isset($_POST['feedback_id']) && isset($_POST['reply_message'])) {
    $feedback_id = intval($_POST['feedback_id']);
    $reply_message = trim($_POST['reply_message']);
    $admin_id = $_SESSION['school_admin_id'];

    if (!empty($reply_message)) {
        // Insert reply into feedback_replies table
        $stmt = $conn->prepare("INSERT INTO feedback_replies (feedback_id, admin_id, reply_message, created_at) VALUES (?, ?, ?, NOW())");
        $stmt->bind_param('iis', $feedback_id, $admin_id, $reply_message);

        if ($stmt->execute()) {
            $_SESSION['parent_success'] = 'Reply sent successfully!';
        } else {
            $_SESSION['parent_error'] = 'Failed to send reply: ' . $conn->error;
        }
        $stmt->close();
    } else {
        $_SESSION['parent_error'] = 'Reply message cannot be empty.';
    }

    header('Location: parents.php');
    exit;
}

// Create necessary tables if they don't exist
try {
    // Parents table
    $result = $conn->query("SHOW TABLES LIKE 'parents'");
    if ($result->num_rows == 0) {
        $conn->query("CREATE TABLE IF NOT EXISTS parents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            school_id INT NOT NULL,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE
        )");
    }

    // Parent feedback table
    $result = $conn->query("SHOW TABLES LIKE 'parent_feedback'");
    if ($result->num_rows == 0) {
        $conn->query("CREATE TABLE IF NOT EXISTS parent_feedback (
            id INT AUTO_INCREMENT PRIMARY KEY,
            parent_id INT NOT NULL,
            school_id INT NOT NULL,
            feedback_text TEXT NOT NULL,
            sentiment_score DECIMAL(3, 2),
            sentiment_label ENUM('positive', 'neutral', 'negative'),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES parents(id) ON DELETE CASCADE,
            FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE
        )");
    }

    // Feedback replies table
    $result = $conn->query("SHOW TABLES LIKE 'feedback_replies'");
    if ($result->num_rows == 0) {
        $conn->query("CREATE TABLE IF NOT EXISTS feedback_replies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            feedback_id INT NOT NULL,
            admin_id INT NOT NULL,
            reply_message TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (feedback_id) REFERENCES parent_feedback(id) ON DELETE CASCADE
        )");
    }

} catch (Exception $e) {
    $_SESSION['parent_error'] = "Database error: " . $e->getMessage();
}

// Fetch all parents for this school
$parents = [];
try {
    $stmt = $conn->prepare('SELECT * FROM parents WHERE school_id = ? ORDER BY created_at DESC');
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $parents = $result->fetch_all(MYSQLI_ASSOC);
    $stmt->close();
} catch (Exception $e) {
    $_SESSION['parent_error'] = "Error fetching parents: " . $e->getMessage();
}

// Fetch parent feedback with replies
$feedback = [];
try {
    $stmt = $conn->prepare('
        SELECT
            pf.id,
            pf.feedback_text,
            pf.sentiment_label,
            pf.created_at,
            p.name as parent_name,
            p.email as parent_email,
            p.id as parent_id,
            fr.reply_message,
            fr.created_at as reply_date
        FROM parent_feedback pf
        JOIN parents p ON pf.parent_id = p.id
        LEFT JOIN feedback_replies fr ON pf.id = fr.feedback_id
        WHERE pf.school_id = ?
        ORDER BY pf.created_at DESC, fr.created_at ASC
    ');
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $feedback_data = [];
    while ($row = $result->fetch_assoc()) {
        $feedback_id = $row['id'];
        if (!isset($feedback_data[$feedback_id])) {
            $feedback_data[$feedback_id] = [
                'id' => $row['id'],
                'feedback_text' => $row['feedback_text'],
                'sentiment_label' => $row['sentiment_label'],
                'created_at' => $row['created_at'],
                'parent_name' => $row['parent_name'],
                'parent_email' => $row['parent_email'],
                'parent_id' => $row['parent_id'],
                'replies' => []
            ];
        }

        if ($row['reply_message']) {
            $feedback_data[$feedback_id]['replies'][] = [
                'reply_message' => $row['reply_message'],
                'reply_date' => $row['reply_date']
            ];
        }
    }
    $feedback = array_values($feedback_data);
    $stmt->close();
} catch (Exception $e) {
    $_SESSION['parent_error'] = "Error fetching feedback: " . $e->getMessage();
}

// Get school info
$school_info = [];
try {
    $stmt = $conn->prepare('SELECT name, logo, address, phone, email FROM schools WHERE id = ?');
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $school_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
} catch (Exception $e) {
    error_log("Error fetching school info: " . $e->getMessage());
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Parents - <?php echo htmlspecialchars($school_info['name'] ?? 'School'); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: <?php echo PRIMARY_COLOR ?? '#00704a'; ?>;
            --footer-color: <?php echo FOOTER_COLOR ?? '#f8c301'; ?>;
            --accent-color: <?php echo ACCENT_COLOR ?? '#00704a'; ?>;
            --light-color: #ffffff;
            --dark-color: #333333;
            --gray-color: #f5f5f5;
            --border-color: #e0e0e0;
            --danger-color: #f44336;
            --sidebar-width: 250px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --radius-sm: 4px;
            --radius-md: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--gray-color);
            min-height: 100vh;
            display: flex;
        }

        /* Search Box Styles */
        .search-container {
            margin-bottom: 1.5rem;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.5rem 1rem;
            box-shadow: var(--shadow-sm);
        }

        .search-icon {
            color: var(--primary-color);
            margin-right: 0.5rem;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 1rem;
            padding: 0.25rem 0;
        }

        .search-clear {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .search-clear:hover {
            color: var(--danger-color);
        }

        .empty-search-results {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .empty-search-results i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #ccc;
        }
        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: var(--light-color);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--light-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .school-logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .school-logo, .school-logo-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .school-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .school-logo-placeholder i {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .sidebar-logo span {
            color: var(--footer-color);
        }

        .sidebar-user {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--accent-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: white;
            font-weight: bold;
        }

        .user-info h3 {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }

        .user-info p {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-heading {
            padding: 0.5rem 1.5rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.6;
        }

        .menu-item {
            padding: 0.8rem 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: var(--accent-color);
        }

        .menu-item i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .menu-item a {
            color: var(--light-color);
            text-decoration: none;
            font-weight: 500;
            flex: 1;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb span {
            margin: 0 0.5rem;
            color: #999;
        }
        /* Card Styles */
        .card {
            background-color: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-header {
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 1.2rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 112, 74, 0.1);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            border: none;
            font-size: 1rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--accent-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid transparent;
        }

        .alert-success {
            background-color: #e8f5e9;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .alert-danger {
            background-color: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        /* Table Styles */
        .table-responsive {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table th {
            font-weight: 600;
            color: var(--primary-color);
            background-color: rgba(0, 112, 74, 0.05);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .action-btns {
            display: flex;
            gap: 0.5rem;
        }

        .btn-icon {
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            text-decoration: none;
        }

        .btn-icon.edit {
            background-color: var(--primary-color);
        }

        .btn-icon.delete {
            background-color: var(--danger-color);
        }

        .btn-icon.reply {
            background-color: #2196f3;
        }

        /* Feedback Styles */
        .feedback-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .feedback-header {
            background-color: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .feedback-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sentiment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .sentiment-positive {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .sentiment-negative {
            background-color: #ffebee;
            color: #c62828;
        }

        .sentiment-neutral {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .feedback-content {
            padding: 1rem;
        }

        .feedback-text {
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .replies-section {
            border-top: 1px solid var(--border-color);
            background-color: #f8f9fa;
        }

        .reply-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--border-color);
            background-color: #e3f2fd;
        }

        .reply-item:last-child {
            border-bottom: none;
        }

        .reply-meta {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 0.5rem;
        }

        .reply-form {
            padding: 1rem;
            background-color: #f8f9fa;
            border-top: 1px solid var(--border-color);
        }

        .reply-form textarea {
            width: 100%;
            min-height: 80px;
            margin-bottom: 0.5rem;
            resize: vertical;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem;
        }

        .empty-icon {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 1rem;
        }

        .empty-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 1.5rem;
        }

        /* Tabs */
        .tab-container {
            margin-bottom: 2rem;
        }

        .tab-nav {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 1.5rem;
        }

        .tab-btn {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Export button */
        .export-btn:hover {
            background-color: #e9ecef !important;
            border-color: #adb5bd !important;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .sidebar {
                width: 70px;
                overflow: visible;
            }

            .sidebar-header, .sidebar-user, .menu-heading {
                display: none;
            }

            .menu-item {
                padding: 1rem 0;
                justify-content: center;
            }

            .menu-item i {
                margin-right: 0;
                font-size: 1.3rem;
            }

            .menu-item a span {
                display: none;
            }

            .main-content {
                margin-left: 70px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .tab-nav {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-users"></i> Manage Parents & Feedback</h1>
            <div class="breadcrumb">
                <a href="dashboard.php">Home</a>
                <span>/</span>
                <span>Parents</span>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (isset($_SESSION['parent_success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php
                echo $_SESSION['parent_success'];
                unset($_SESSION['parent_success']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['parent_error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php
                echo $_SESSION['parent_error'];
                unset($_SESSION['parent_error']);
                ?>
            </div>
        <?php endif; ?>

        <!-- Tab Navigation -->
        <div class="tab-container">
            <div class="tab-nav">
                <button class="tab-btn active" onclick="switchTab('parents')">
                    <i class="fas fa-users"></i> Parents Management
                </button>
                <button class="tab-btn" onclick="switchTab('feedback')">
                    <i class="fas fa-comments"></i> Parent Feedback
                </button>
            </div>

            <!-- Parents Tab -->
            <div id="parents-tab" class="tab-content active">
                <!-- Parents List Card -->
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-list"></i> All Parents</h2>
                        <!-- Action buttons positioned at the right -->
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <button onclick="exportParentData()" class="btn export-btn" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.25rem; font-size: 0.9rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; transition: all 0.3s ease;">
                                <i class="fas fa-download"></i> Export List
                            </button>
                            <button class="btn btn-primary" onclick="openModal('addParentModal')" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; font-size: 0.9rem; border-radius: 6px; transition: all 0.3s ease;">
                                <i class="fas fa-user-plus"></i> Add Parent
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($parents) > 0): ?>
                            <!-- Search Box -->
                            <div class="search-container" data-table="parents-table">
                                <div class="search-box">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="search-input" placeholder="Search parents...">
                                    <button type="button" class="search-clear" onclick="clearSearch('parents-table')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <br>
                            <div class="table-responsive">
                                <table class="data-table" id="parents-table">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Address</th>
                                            <th>Date Added</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($parents as $parent): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($parent['name']); ?></td>
                                                <td><?php echo htmlspecialchars($parent['email']); ?></td>
                                                <td><?php echo htmlspecialchars($parent['phone'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($parent['address'] ?? 'N/A'); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($parent['created_at'])); ?></td>
                                                <td>
                                                    <div class="action-btns">
                                                        <a href="#" class="btn-icon edit" data-id="<?php echo $parent['id']; ?>" title="Edit"><i class="fas fa-edit"></i></a>
                                                        <a href="#" class="btn-icon delete" data-id="<?php echo $parent['id']; ?>" title="Delete"><i class="fas fa-trash"></i></a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>

                                <!-- Empty search results message -->
                                <div id="parents-table-empty-search" class="empty-search-results" style="display: none;">
                                    <i class="fas fa-search"></i>
                                    <p>No parents found matching your search.</p>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-user-slash"></i></div>
                                <div class="empty-text">No parents found</div>
                                <p>Start by adding a new parent using the Add Parent button above.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Feedback Tab -->
            <div id="feedback-tab" class="tab-content">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-comments"></i> Parent Feedback & Messages</h2>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <button onclick="exportFeedbackData()" class="btn export-btn" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.25rem; font-size: 0.9rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; transition: all 0.3s ease;">
                                <i class="fas fa-download"></i> Export Feedback
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($feedback) > 0): ?>
                            <!-- Search Box for Feedback -->
                            <div class="search-container" data-table="feedback-container">
                                <div class="search-box">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="search-input" placeholder="Search feedback...">
                                    <button type="button" class="search-clear" onclick="clearFeedbackSearch()">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <br>

                            <div id="feedback-container">
                                <?php foreach ($feedback as $item): ?>
                                    <div class="feedback-item" data-feedback-text="<?php echo htmlspecialchars($item['feedback_text']); ?>" data-parent-name="<?php echo htmlspecialchars($item['parent_name']); ?>">
                                        <div class="feedback-header">
                                            <div class="feedback-meta">
                                                <strong><?php echo htmlspecialchars($item['parent_name']); ?></strong>
                                                <span class="text-muted">(<?php echo htmlspecialchars($item['parent_email']); ?>)</span>
                                                <span class="sentiment-badge sentiment-<?php echo $item['sentiment_label'] ?? 'neutral'; ?>">
                                                    <?php echo ucfirst($item['sentiment_label'] ?? 'neutral'); ?>
                                                </span>
                                            </div>
                                            <small class="text-muted"><?php echo date('M d, Y H:i', strtotime($item['created_at'])); ?></small>
                                        </div>

                                        <div class="feedback-content">
                                            <div class="feedback-text">
                                                <?php echo nl2br(htmlspecialchars($item['feedback_text'])); ?>
                                            </div>

                                            <?php if (!empty($item['replies'])): ?>
                                                <div class="replies-section">
                                                    <h6 style="padding: 0.75rem 1rem; margin: 0; background-color: #e9ecef; font-weight: 600;">
                                                        <i class="fas fa-reply"></i> Admin Replies
                                                    </h6>
                                                    <?php foreach ($item['replies'] as $reply): ?>
                                                        <div class="reply-item">
                                                            <div class="reply-meta">
                                                                <i class="fas fa-user-shield"></i> School Admin • <?php echo date('M d, Y H:i', strtotime($reply['reply_date'])); ?>
                                                            </div>
                                                            <div><?php echo nl2br(htmlspecialchars($reply['reply_message'])); ?></div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Reply Form -->
                                            <div class="reply-form">
                                                <form method="POST" action="parents.php">
                                                    <input type="hidden" name="feedback_id" value="<?php echo $item['id']; ?>">
                                                    <textarea name="reply_message" class="form-control" placeholder="Type your reply to this parent..." required></textarea>
                                                    <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem;">
                                                        <button type="submit" name="reply_feedback" class="btn btn-primary btn-sm">
                                                            <i class="fas fa-reply"></i> Send Reply
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Empty search results message -->
                            <div id="feedback-empty-search" class="empty-search-results" style="display: none;">
                                <i class="fas fa-search"></i>
                                <p>No feedback found matching your search.</p>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-comment-slash"></i></div>
                                <div class="empty-text">No feedback received yet</div>
                                <p>Parent feedback and messages will appear here when submitted.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Parent Modal -->
    <div id="addParentModal" class="modal">
        <div class="modal-content" style="max-width: 600px; border-radius: 12px; overflow: hidden;">
            <div class="modal-header" style="background: linear-gradient(135deg, #00704a, #2563eb); color: white; padding: 1.5rem 2rem; display: flex; justify-content: space-between; align-items: center;">
                <h2 style="margin: 0; display: flex; align-items: center; gap: 0.5rem; font-size: 1.25rem;"><i class="fas fa-user-plus"></i> Add New Parent</h2>
                <span class="close-modal" onclick="closeModal('addParentModal')" style="color: white; font-size: 1.5rem; cursor: pointer; background: none; border: none;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 2rem; background: white;">
                <?php if (isset($_SESSION['parent_error'])): ?>
                    <div class="alert alert-danger" style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
                        <i class="fas fa-exclamation-circle"></i>
                        <?php echo $_SESSION['parent_error']; unset($_SESSION['parent_error']); ?>
                    </div>
                <?php endif; ?>

                <form action="add_parent.php" method="POST" class="modal-form" id="parentForm">
                    <div class="form-section" style="margin-bottom: 2rem;">
                        <div class="section-header" style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 1px solid #e9ecef;">
                            <div style="width: 40px; height: 40px; background: #00704a; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white;">
                                <i class="fas fa-user"></i>
                            </div>
                            <h3 style="margin: 0; color: #00704a; font-size: 1.1rem;">Parent Information</h3>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                            <div style="margin-bottom: 1rem;">
                                <label for="parent_name" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #333;">
                                    Full Name <span style="color: #dc3545;">*</span>
                                </label>
                                <input type="text" id="parent_name" name="name" class="form-control"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 0.95rem;" required>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label for="parent_email" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #333;">
                                    Email Address <span style="color: #dc3545;">*</span>
                                </label>
                                <input type="email" id="parent_email" name="email" class="form-control"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 0.95rem;" required>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label for="parent_phone" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #333;">
                                    Phone Number
                                </label>
                                <input type="tel" id="parent_phone" name="phone" class="form-control"
                                       style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 0.95rem;">
                            </div>
                            <div style="margin-bottom: 1rem; grid-column: 1 / -1;">
                                <label for="parent_address" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #333;">
                                    Address
                                </label>
                                <textarea id="parent_address" name="address" class="form-control" rows="3"
                                          style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 6px; font-size: 0.95rem; resize: vertical;"></textarea>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; justify-content: flex-start; gap: 1rem; margin-top: 2rem; padding-top: 1.5rem; border-top: 1px solid #e9ecef;">
                        <button type="submit" id="saveParentBtn"
                                style="background: #00704a; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; transition: background-color 0.3s ease;"
                                onmouseover="this.style.background='#2563eb'" onmouseout="this.style.background='#00704a'">
                            <i class="fas fa-save"></i> Save Parent
                        </button>
                        <button type="button" onclick="closeModal('addParentModal')"
                                style="background: #6c757d; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 6px; font-weight: 500; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            width: 80%;
            max-width: 800px;
            animation: modalFadeIn 0.3s;
        }

        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .text-muted {
            color: #6c757d;
        }
    </style>

    <!-- Include search.js for search functionality -->
    <script src="js/search.js"></script>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Export parent data function
        function exportParentData() {
            const table = document.getElementById('parents-table');
            const rows = table.querySelectorAll('tbody tr:not([style*="display: none"])');

            if (rows.length === 0) {
                alert('No parents to export');
                return;
            }

            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Name,Email,Phone,Address,Date Added\n";

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0].textContent.trim(),
                    cells[1].textContent.trim(),
                    cells[2].textContent.trim(),
                    cells[3].textContent.trim(),
                    cells[4].textContent.trim()
                ];
                csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'parents_list.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Export feedback data function
        function exportFeedbackData() {
            const feedbackItems = document.querySelectorAll('.feedback-item:not([style*="display: none"])');

            if (feedbackItems.length === 0) {
                alert('No feedback to export');
                return;
            }

            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Parent Name,Email,Feedback,Sentiment,Date,Replies Count\n";

            feedbackItems.forEach(item => {
                const parentName = item.querySelector('.feedback-meta strong').textContent.trim();
                const email = item.querySelector('.feedback-meta .text-muted').textContent.replace(/[()]/g, '').trim();
                const feedback = item.querySelector('.feedback-text').textContent.trim();
                const sentiment = item.querySelector('.sentiment-badge').textContent.trim();
                const date = item.querySelector('.feedback-header small').textContent.trim();
                const repliesCount = item.querySelectorAll('.reply-item').length;

                const rowData = [parentName, email, feedback, sentiment, date, repliesCount];
                csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'parent_feedback.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Feedback search functionality
        function clearFeedbackSearch() {
            const searchInput = document.querySelector('#feedback-tab .search-input');
            searchInput.value = '';
            filterFeedback('');
        }

        function filterFeedback(searchTerm) {
            const feedbackItems = document.querySelectorAll('.feedback-item');
            const emptyMessage = document.getElementById('feedback-empty-search');
            let visibleCount = 0;

            feedbackItems.forEach(item => {
                const feedbackText = item.getAttribute('data-feedback-text').toLowerCase();
                const parentName = item.getAttribute('data-parent-name').toLowerCase();
                const searchLower = searchTerm.toLowerCase();

                if (feedbackText.includes(searchLower) || parentName.includes(searchLower)) {
                    item.style.display = 'block';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Show/hide empty message
            if (visibleCount === 0 && searchTerm !== '') {
                emptyMessage.style.display = 'block';
            } else {
                emptyMessage.style.display = 'none';
            }
        }

        // Function to open modal
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // Function to close modal
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }
        }

        // Confirm delete parent
        function confirmDeleteParent(parentId) {
            if (confirm('Are you sure you want to delete this parent? This action cannot be undone.')) {
                window.location.href = `parents.php?action=delete&id=${parentId}`;
            }
        }

        // Initialize page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to delete buttons
            const deleteButtons = document.querySelectorAll('a.btn-icon.delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const parentId = this.getAttribute('data-id');
                    confirmDeleteParent(parentId);
                });
            });

            // Add feedback search functionality
            const feedbackSearchInput = document.querySelector('#feedback-tab .search-input');
            if (feedbackSearchInput) {
                feedbackSearchInput.addEventListener('input', function() {
                    filterFeedback(this.value);
                });
            }
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = '';
                }
            });
        }
    </script>
</body>
</html>