<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once '../config/config.php';

session_start();
if (!isset($_SESSION['school_admin_id'])) {
    header('Location: ../login.php');
    exit;
}

$school_id = $_SESSION['school_admin_school_id'];
$admin_id = $_SESSION['school_admin_id'];
$error = '';
$success = '';

// Handle permission request approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && isset($_POST['request_id'])) {
        $request_id = $_POST['request_id'];
        $action = $_POST['action'];
        $response_comment = isset($_POST['response_comment']) ? trim($_POST['response_comment']) : '';
        
        if ($action === 'approve' || $action === 'reject') {
            try {
                $conn = getDbConnection();
                
                // Update the permission request status
                $status = ($action === 'approve') ? 'approved' : 'rejected';
                
                $stmt = $conn->prepare('UPDATE permission_requests 
                                      SET status = ?, response_comment = ?, responded_by = ? 
                                      WHERE id = ? AND EXISTS (
                                          SELECT 1 FROM students s 
                                          WHERE s.id = permission_requests.student_id 
                                          AND s.school_id = ?
                                      )');
                
                if (!$stmt) {
                    throw new Exception("Failed to prepare update query: " . $conn->error);
                }
                
                $stmt->bind_param('ssiii', $status, $response_comment, $admin_id, $request_id, $school_id);
                
                if ($stmt->execute()) {
                    $success = 'Permission request has been ' . ($status === 'approved' ? 'approved' : 'rejected') . ' successfully.';
                } else {
                    $error = 'Failed to update permission request: ' . $stmt->error;
                }
                $stmt->close();
            } catch (Exception $e) {
                $error = 'Error: ' . $e->getMessage();
                error_log("Permission update error: " . $e->getMessage());
            }
        }
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : 'all';
$type_filter = isset($_GET['type']) ? $_GET['type'] : 'all';
$date_filter = isset($_GET['date']) ? $_GET['date'] : 'all';

// Fetch permission request statistics
$stats = [];
try {
    $conn = getDbConnection();

    // Check if tables exist first
    $tables_check = $conn->query("SHOW TABLES LIKE 'permission_requests'");
    if ($tables_check && $tables_check->num_rows > 0) {
        // Get counts by status
        $stats_query = 'SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN pr.status = "pending" THEN 1 ELSE 0 END) as pending,
                        SUM(CASE WHEN pr.status = "approved" THEN 1 ELSE 0 END) as approved,
                        SUM(CASE WHEN pr.status = "rejected" THEN 1 ELSE 0 END) as rejected,
                        SUM(CASE WHEN pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as this_week,
                        SUM(CASE WHEN pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as this_month
                        FROM permission_requests pr
                        JOIN students s ON pr.student_id = s.id
                        WHERE s.school_id = ?';

        $stmt = $conn->prepare($stats_query);
        if ($stmt) {
            $stmt->bind_param('i', $school_id);
            $stmt->execute();
            $stats = $stmt->get_result()->fetch_assoc();
            $stmt->close();
        }
    } else {
        // If permission_requests table doesn't exist, set default stats
        $stats = [
            'total' => 0,
            'pending' => 0,
            'approved' => 0,
            'rejected' => 0,
            'this_week' => 0,
            'this_month' => 0
        ];
    }
} catch (Exception $e) {
    error_log("Permission stats error: " . $e->getMessage());
    // Set default stats on error
    $stats = [
        'total' => 0,
        'pending' => 0,
        'approved' => 0,
        'rejected' => 0,
        'this_week' => 0,
        'this_month' => 0
    ];
}

// Fetch all permission requests for this school with filters
$requests = [];
try {
    $conn = getDbConnection();

    // Check if permission_requests table exists
    $tables_check = $conn->query("SHOW TABLES LIKE 'permission_requests'");
    if (!$tables_check || $tables_check->num_rows == 0) {
        // Table doesn't exist, create it
        $create_table_sql = "CREATE TABLE IF NOT EXISTS permission_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            parent_id INT NOT NULL,
            request_type ENUM('leave', 'medical', 'event', 'other') NOT NULL DEFAULT 'other',
            start_date DATETIME NOT NULL,
            end_date DATETIME NOT NULL,
            reason TEXT NOT NULL,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            response_comment TEXT,
            responded_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
            FOREIGN KEY (parent_id) REFERENCES parents(id) ON DELETE CASCADE,
            FOREIGN KEY (responded_by) REFERENCES school_admins(id) ON DELETE SET NULL
        )";

        $conn->query($create_table_sql);
    }

    // First, check what columns exist in the students table
    $columns_check = $conn->query("SHOW COLUMNS FROM students");
    $available_columns = [];
    if ($columns_check) {
        while ($col = $columns_check->fetch_assoc()) {
            $available_columns[] = $col['Field'];
        }
    }

    // Build the SELECT clause based on available columns
    $student_fields = [
        's.first_name AS student_first_name',
        's.last_name AS student_last_name'
    ];

    // Add optional columns if they exist
    if (in_array('admission_number', $available_columns)) {
        $student_fields[] = 's.admission_number';
    } else if (in_array('reg_number', $available_columns)) {
        $student_fields[] = 's.reg_number AS admission_number';
    } else {
        $student_fields[] = 'CONCAT("STU-", s.id) AS admission_number';
    }

    if (in_array('class', $available_columns)) {
        $student_fields[] = 's.class';
    } else {
        $student_fields[] = '"N/A" AS class';
    }

    if (in_array('status', $available_columns)) {
        $student_fields[] = 's.status as student_status';
    } else {
        $student_fields[] = '"active" AS student_status';
    }

    // Check what columns exist in the parents table
    $parent_columns_check = $conn->query("SHOW COLUMNS FROM parents");
    $available_parent_columns = [];
    if ($parent_columns_check) {
        while ($col = $parent_columns_check->fetch_assoc()) {
            $available_parent_columns[] = $col['Field'];
        }
    }

    // Build parent fields based on available columns
    $parent_fields = [];
    if (in_array('first_name', $available_parent_columns) && in_array('last_name', $available_parent_columns)) {
        $parent_fields[] = 'p.first_name AS parent_first_name';
        $parent_fields[] = 'p.last_name AS parent_last_name';
    } else if (in_array('name', $available_parent_columns)) {
        $parent_fields[] = 'p.name AS parent_first_name';
        $parent_fields[] = '"" AS parent_last_name';
    } else {
        $parent_fields[] = '"Parent" AS parent_first_name';
        $parent_fields[] = '"" AS parent_last_name';
    }

    if (in_array('email', $available_parent_columns)) {
        $parent_fields[] = 'p.email AS parent_email';
    } else {
        $parent_fields[] = '"" AS parent_email';
    }

    if (in_array('phone', $available_parent_columns)) {
        $parent_fields[] = 'p.phone AS parent_phone';
    } else {
        $parent_fields[] = '"" AS parent_phone';
    }

    if (in_array('relationship', $available_parent_columns)) {
        $parent_fields[] = 'p.relationship';
    } else {
        $parent_fields[] = '"guardian" AS relationship';
    }

    // Build WHERE clause based on filters
    $where_conditions = ['s.school_id = ?'];
    $params = [$school_id];
    $param_types = 'i';

    if ($status_filter !== 'all') {
        $where_conditions[] = 'pr.status = ?';
        $params[] = $status_filter;
        $param_types .= 's';
    }

    if ($type_filter !== 'all') {
        $where_conditions[] = 'pr.request_type = ?';
        $params[] = $type_filter;
        $param_types .= 's';
    }

    if ($date_filter !== 'all') {
        switch ($date_filter) {
            case 'today':
                $where_conditions[] = 'DATE(pr.created_at) = CURDATE()';
                break;
            case 'week':
                $where_conditions[] = 'pr.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
                break;
            case 'month':
                $where_conditions[] = 'pr.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
                break;
        }
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Combine all fields
    $all_fields = array_merge(['pr.*'], $student_fields, $parent_fields, ['sa.full_name AS admin_name']);

    // Join with students, parents tables to get names
    $query = 'SELECT ' . implode(', ', $all_fields) . '
              FROM permission_requests pr
              JOIN students s ON pr.student_id = s.id
              JOIN parents p ON pr.parent_id = p.id
              LEFT JOIN school_admins sa ON pr.responded_by = sa.id
              WHERE ' . $where_clause . '
              ORDER BY pr.created_at DESC';

    $stmt = $conn->prepare($query);

    if (!$stmt) {
        throw new Exception("Failed to prepare query: " . $conn->error);
    }

    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $requests[] = $row;
    }

    $stmt->close();
} catch (Exception $e) {
    $error = 'Error: ' . $e->getMessage();
    error_log("Permission fetch error: " . $e->getMessage());

    // If there's an error, try a simpler approach
    try {
        // Check if we can at least get basic permission requests
        $simple_query = "SELECT pr.*,
                        'Student' AS student_first_name,
                        '' AS student_last_name,
                        CONCAT('STU-', pr.student_id) AS admission_number,
                        'N/A' AS class,
                        'active' AS student_status,
                        'Parent' AS parent_first_name,
                        '' AS parent_last_name,
                        '' AS parent_email,
                        '' AS parent_phone,
                        'guardian' AS relationship,
                        '' AS admin_name
                        FROM permission_requests pr
                        WHERE EXISTS (SELECT 1 FROM students s WHERE s.id = pr.student_id AND s.school_id = ?)
                        ORDER BY pr.created_at DESC";

        $stmt = $conn->prepare($simple_query);
        if ($stmt) {
            $stmt->bind_param('i', $school_id);
            $stmt->execute();
            $result = $stmt->get_result();

            while ($row = $result->fetch_assoc()) {
                $requests[] = $row;
            }
            $stmt->close();
            $error = ''; // Clear the error if this works
        }
    } catch (Exception $e2) {
        error_log("Simple permission fetch also failed: " . $e2->getMessage());
        // Keep the original error message
    }
}

// Get school info for sidebar
$school_info = [];
try {
    $conn = getDbConnection();
    $stmt = $conn->prepare('SELECT name, logo, address, phone, email FROM schools WHERE id = ?');
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $school_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
} catch (Exception $e) {
    error_log("Error fetching school info: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permission Requests - <?php echo htmlspecialchars($school_info['name'] ?? 'School'); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="enhanced-form-styles.css">
    <style>
        :root {
            --primary-color: <?php echo PRIMARY_COLOR ?? '#00704a'; ?>;
            --footer-color: <?php echo FOOTER_COLOR ?? '#f8c301'; ?>;
            --accent-color: <?php echo ACCENT_COLOR ?? '#00704a'; ?>;
            --light-color: #ffffff;
            --dark-color: #333333;
            --gray-color: #f5f5f5;
            --border-color: #e0e0e0;
            --sidebar-width: 250px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--gray-color);
            min-height: 100vh;
            display: flex;
        }

        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: var(--light-color);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .school-logo-container {
            margin-bottom: 1rem;
        }

        .school-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--footer-color);
        }

        .school-logo-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--footer-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border: 3px solid var(--footer-color);
        }

        .school-logo-placeholder i {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--light-color);
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .sidebar-logo span {
            color: var(--footer-color);
        }

        .sidebar-user {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--accent-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: white;
            font-weight: bold;
        }

        .user-info h3 {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }

        .user-info p {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-heading {
            padding: 0.5rem 1.5rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.6;
        }

        .menu-item {
            padding: 0.8rem 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }

        .menu-item:hover, .menu-item.active {
            background-color: var(--accent-color);
        }

        .menu-item i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .menu-item a {
            color: var(--light-color);
            text-decoration: none;
            font-weight: 500;
            flex: 1;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 2rem;
        }

        .page-header {
            margin-bottom: 2rem;
        }

        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .breadcrumb span {
            margin: 0 0.5rem;
            color: #999;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: var(--light-color);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-color);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card.pending {
            border-left-color: #ffc107;
        }

        .stat-card.approved {
            border-left-color: #4caf50;
        }

        .stat-card.rejected {
            border-left-color: #f44336;
        }

        .stat-card.total {
            border-left-color: var(--primary-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .stat-title {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .stat-icon.pending {
            background-color: #ffc107;
        }

        .stat-icon.approved {
            background-color: #4caf50;
        }

        .stat-icon.rejected {
            background-color: #f44336;
        }

        .stat-icon.total {
            background-color: var(--primary-color);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .stat-description {
            font-size: 0.8rem;
            color: #888;
        }

        /* Permission-specific styles */
        .permission-card {
            background-color: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .permission-header {
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgba(0, 112, 74, 0.02);
        }

        .permission-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .permission-date {
            font-size: 0.9rem;
            color: #777;
        }

        .permission-details {
            padding: 1.5rem;
        }

        .permission-details p {
            margin: 0.5rem 0;
        }

        .permission-reason {
            background-color: var(--gray-color);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid var(--primary-color);
        }

        .permission-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .btn-approve {
            background-color: #4caf50;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-approve:hover {
            background-color: #45a049;
            transform: translateY(-1px);
        }

        .btn-reject {
            background-color: #f44336;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-reject:hover {
            background-color: #da190b;
            transform: translateY(-1px);
        }

        .status-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .filter-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background-color: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            align-items: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .filter-controls select {
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            background-color: white;
            min-width: 150px;
            transition: border-color 0.3s ease;
        }

        .filter-controls select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 112, 74, 0.1);
        }

        .filter-actions {
            display: flex;
            gap: 0.5rem;
            margin-left: auto;
        }

        .btn-filter {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-filter.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-filter.primary:hover {
            background-color: var(--accent-color);
            transform: translateY(-1px);
        }

        .btn-filter.secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-filter.secondary:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
        }

        .parent-info, .student-info {
            margin-bottom: 1rem;
        }

        .parent-info i, .student-info i {
            width: 20px;
            color: var(--primary-color);
            margin-right: 0.5rem;
        }

        .date-range {
            display: flex;
            justify-content: space-between;
            background-color: var(--gray-color);
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-size: 0.9rem;
            border: 1px solid var(--border-color);
        }

        .response-form {
            margin-top: 1rem;
            display: none;
            background-color: var(--gray-color);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .response-form textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 1rem;
            font-family: 'Poppins', sans-serif;
            resize: vertical;
            min-height: 80px;
        }

        .response-comment {
            background-color: var(--gray-color);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            font-style: italic;
            border: 1px solid var(--border-color);
        }

        .btn-submit, .btn-cancel {
            padding: 0.5rem 1rem;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            margin-right: 0.5rem;
            transition: all 0.3s;
        }

        .btn-submit {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-submit:hover {
            background-color: var(--accent-color);
        }

        .btn-cancel {
            background-color: #6c757d;
            color: white;
        }

        .btn-cancel:hover {
            background-color: #5a6268;
        }

        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid transparent;
        }

        .alert-success {
            background-color: #e8f5e9;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .alert-danger {
            background-color: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        .alert-info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1565c0;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .sidebar {
                width: 70px;
                overflow: visible;
            }

            .sidebar-header, .sidebar-user, .menu-heading {
                display: none;
            }

            .menu-item {
                padding: 1rem 0;
                justify-content: center;
            }

            .menu-item i {
                margin-right: 0;
                font-size: 1.3rem;
            }

            .menu-item a {
                display: none;
            }

            .main-content {
                margin-left: 70px;
            }
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .filter-controls {
                flex-direction: column;
            }

            .date-range {
                flex-direction: column;
                gap: 0.5rem;
            }

            .permission-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-clipboard-check"></i> Permission Requests</h1>
            <div class="breadcrumb">
                <a href="dashboard.php">Home</a>
                <span>/</span>
                <span>Permission Requests</span>
            </div>
        </div>

        <!-- Alert Messages -->
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-header">
                    <span class="stat-title">Total Requests</span>
                    <div class="stat-icon total">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                </div>
                <div class="stat-number"><?php echo $stats['total'] ?? 0; ?></div>
                <div class="stat-description">All permission requests</div>
            </div>

            <div class="stat-card pending">
                <div class="stat-header">
                    <span class="stat-title">Pending</span>
                    <div class="stat-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-number"><?php echo $stats['pending'] ?? 0; ?></div>
                <div class="stat-description">Awaiting review</div>
            </div>

            <div class="stat-card approved">
                <div class="stat-header">
                    <span class="stat-title">Approved</span>
                    <div class="stat-icon approved">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-number"><?php echo $stats['approved'] ?? 0; ?></div>
                <div class="stat-description">Approved requests</div>
            </div>

            <div class="stat-card rejected">
                <div class="stat-header">
                    <span class="stat-title">Rejected</span>
                    <div class="stat-icon rejected">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
                <div class="stat-number"><?php echo $stats['rejected'] ?? 0; ?></div>
                <div class="stat-description">Rejected requests</div>
            </div>
        </div>

        <!-- Enhanced Filter Controls -->
        <div class="filter-controls">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-filter" style="color: var(--primary-color);"></i>
                <strong>Filter Requests:</strong>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select id="statusFilter" name="status">
                    <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Type</label>
                <select id="typeFilter" name="type">
                    <option value="all" <?php echo $type_filter === 'all' ? 'selected' : ''; ?>>All Types</option>
                    <option value="leave" <?php echo $type_filter === 'leave' ? 'selected' : ''; ?>>Leave of Absence</option>
                    <option value="medical" <?php echo $type_filter === 'medical' ? 'selected' : ''; ?>>Medical</option>
                    <option value="event" <?php echo $type_filter === 'event' ? 'selected' : ''; ?>>Event</option>
                    <option value="other" <?php echo $type_filter === 'other' ? 'selected' : ''; ?>>Other</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <select id="dateFilter" name="date">
                    <option value="all" <?php echo $date_filter === 'all' ? 'selected' : ''; ?>>All Time</option>
                    <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>>Today</option>
                    <option value="week" <?php echo $date_filter === 'week' ? 'selected' : ''; ?>>This Week</option>
                    <option value="month" <?php echo $date_filter === 'month' ? 'selected' : ''; ?>>This Month</option>
                </select>
            </div>

            <div class="filter-actions">
                <button type="button" class="btn-filter primary" onclick="applyFilters()">
                    <i class="fas fa-search"></i> Apply Filters
                </button>
                <button type="button" class="btn-filter secondary" onclick="clearFilters()">
                    <i class="fas fa-times"></i> Clear
                </button>
            </div>
        </div>

        <!-- Permissions Container -->
        <div class="permissions-container">
            <?php if (empty($requests)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <p>No permission requests found.</p>
                </div>
            <?php else: ?>
                <?php foreach ($requests as $request): ?>
                    <div class="permission-card" data-status="<?php echo htmlspecialchars($request['status']); ?>" data-type="<?php echo htmlspecialchars($request['request_type']); ?>">
                        <div class="permission-header">
                            <div class="permission-title">
                                <i class="fas fa-clipboard-list"></i>
                                <?php echo ucfirst(htmlspecialchars($request['request_type'])); ?> Request
                                <span class="status-badge status-<?php echo htmlspecialchars($request['status']); ?>">
                                    <?php echo ucfirst(htmlspecialchars($request['status'])); ?>
                                </span>
                            </div>
                            <div class="permission-date">
                                <i class="fas fa-clock"></i>
                                Submitted: <?php echo date('M d, Y h:i A', strtotime($request['created_at'])); ?>
                            </div>
                        </div>

                        <div class="permission-details">
                            <div class="info-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1rem;">
                                <div class="student-info">
                                    <h4 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1rem;">
                                        <i class="fas fa-user-graduate"></i> Student Information
                                    </h4>
                                    <p><strong>Name:</strong> <?php echo htmlspecialchars($request['student_first_name'] . ' ' . $request['student_last_name']); ?></p>
                                    <p><strong>Admission No:</strong> <?php echo htmlspecialchars($request['admission_number']); ?></p>
                                    <p><strong>Class:</strong> <?php echo htmlspecialchars($request['class'] ?? 'N/A'); ?></p>
                                    <p><strong>Status:</strong>
                                        <span class="status-badge status-<?php echo $request['student_status']; ?>">
                                            <?php echo ucfirst($request['student_status']); ?>
                                        </span>
                                    </p>
                                </div>

                                <div class="parent-info">
                                    <h4 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1rem;">
                                        <i class="fas fa-user"></i> Parent/Guardian Information
                                    </h4>
                                    <p><strong>Name:</strong> <?php echo htmlspecialchars($request['parent_first_name'] . ' ' . $request['parent_last_name']); ?></p>
                                    <p><strong>Relationship:</strong> <?php echo ucfirst(htmlspecialchars($request['relationship'] ?? 'Guardian')); ?></p>
                                    <p><strong>Email:</strong>
                                        <a href="mailto:<?php echo htmlspecialchars($request['parent_email']); ?>" style="color: var(--primary-color);">
                                            <?php echo htmlspecialchars($request['parent_email']); ?>
                                        </a>
                                    </p>
                                    <p><strong>Phone:</strong>
                                        <a href="tel:<?php echo htmlspecialchars($request['parent_phone']); ?>" style="color: var(--primary-color);">
                                            <?php echo htmlspecialchars($request['parent_phone']); ?>
                                        </a>
                                    </p>
                                </div>
                            </div>

                            <div class="request-details" style="margin-bottom: 1rem;">
                                <h4 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1rem;">
                                    <i class="fas fa-info-circle"></i> Request Details
                                </h4>
                                <div class="date-range">
                                    <span><i class="fas fa-calendar-alt"></i> <strong>From:</strong> <?php echo date('M d, Y h:i A', strtotime($request['start_date'])); ?></span>
                                    <span><i class="fas fa-calendar-check"></i> <strong>To:</strong> <?php echo date('M d, Y h:i A', strtotime($request['end_date'])); ?></span>
                                </div>

                                <?php
                                $duration = (strtotime($request['end_date']) - strtotime($request['start_date'])) / (60 * 60 * 24);
                                $duration = round($duration, 1);
                                ?>
                                <p style="margin-top: 0.5rem;"><strong>Duration:</strong> <?php echo $duration; ?> day(s)</p>
                            </div>

                            <div class="permission-reason">
                                <h4 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1rem;">
                                    <i class="fas fa-comment-alt"></i> Reason for Request
                                </h4>
                                <p><?php echo nl2br(htmlspecialchars($request['reason'])); ?></p>
                            </div>
                                
                                <?php if (!empty($request['response_comment']) && ($request['status'] === 'approved' || $request['status'] === 'rejected')): ?>
                                    <div class="response-comment">
                                        <p><strong>Response from <?php echo htmlspecialchars($request['admin_name']); ?>:</strong></p>
                                        <p><?php echo nl2br(htmlspecialchars($request['response_comment'])); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($request['status'] === 'pending'): ?>
                                <div class="permission-actions">
                                    <button class="btn-approve" onclick="showResponseForm(this, 'approve', <?php echo $request['id']; ?>)">
                                        <i class="fas fa-check"></i> Approve
                                    </button>
                                    <button class="btn-reject" onclick="showResponseForm(this, 'reject', <?php echo $request['id']; ?>)">
                                        <i class="fas fa-times"></i> Reject
                                    </button>

                                    <div class="response-form" id="response-form-<?php echo $request['id']; ?>">
                                        <form method="POST">
                                            <input type="hidden" name="request_id" value="<?php echo $request['id']; ?>">
                                            <input type="hidden" name="action" id="action-<?php echo $request['id']; ?>" value="">

                                            <textarea name="response_comment" placeholder="Add a comment (optional)" rows="3"></textarea>

                                            <div>
                                                <button type="submit" class="btn-submit">
                                                    <i class="fas fa-paper-plane"></i> Confirm
                                                </button>
                                                <button type="button" class="btn-cancel" onclick="hideResponseForm(<?php echo $request['id']; ?>)">
                                                    <i class="fas fa-times"></i> Cancel
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
        // Show response form when approve/reject button is clicked
        function showResponseForm(button, action, requestId) {
            // Hide all other forms first
            document.querySelectorAll('.response-form').forEach(form => {
                form.style.display = 'none';
            });

            // Show this form
            const form = document.getElementById('response-form-' + requestId);
            form.style.display = 'block';

            // Set the action
            document.getElementById('action-' + requestId).value = action;

            // Scroll to the form
            form.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Hide response form
        function hideResponseForm(requestId) {
            document.getElementById('response-form-' + requestId).style.display = 'none';
        }

        // Apply filters function
        function applyFilters() {
            const status = document.getElementById('statusFilter').value;
            const type = document.getElementById('typeFilter').value;
            const date = document.getElementById('dateFilter').value;

            // Build URL with filter parameters
            const params = new URLSearchParams();
            if (status !== 'all') params.append('status', status);
            if (type !== 'all') params.append('type', type);
            if (date !== 'all') params.append('date', date);

            // Redirect with filters
            const url = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
            window.location.href = url;
        }

        // Clear all filters
        function clearFilters() {
            window.location.href = window.location.pathname;
        }

        // Auto-refresh for new requests (every 30 seconds)
        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                // Only refresh if no forms are open
                const openForms = document.querySelectorAll('.response-form[style*="block"]');
                if (openForms.length === 0) {
                    // Check for new requests via AJAX
                    checkForNewRequests();
                }
            }, 30000); // 30 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        // Check for new requests
        function checkForNewRequests() {
            fetch(window.location.href, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                // Parse the response to check for new requests
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newStats = doc.querySelector('.stats-grid');
                const currentStats = document.querySelector('.stats-grid');

                if (newStats && currentStats) {
                    // Update statistics if they've changed
                    const newPending = newStats.querySelector('.stat-card.pending .stat-number').textContent;
                    const currentPending = currentStats.querySelector('.stat-card.pending .stat-number').textContent;

                    if (newPending !== currentPending) {
                        // Show notification for new requests
                        showNotification('New permission request(s) received!', 'info');
                        // Update the stats
                        currentStats.innerHTML = newStats.innerHTML;
                    }
                }
            })
            .catch(error => {
                console.log('Auto-refresh error:', error);
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            `;
            notification.innerHTML = `
                <i class="fas fa-${type === 'info' ? 'info-circle' : 'check-circle'}"></i>
                ${message}
                <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Add CSS for notification animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Start auto-refresh
            startAutoRefresh();

            // Stop auto-refresh when page is hidden
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    stopAutoRefresh();
                } else {
                    startAutoRefresh();
                }
            });

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl+R or F5 to refresh
                if ((e.ctrlKey && e.key === 'r') || e.key === 'F5') {
                    e.preventDefault();
                    window.location.reload();
                }

                // Escape to close any open forms
                if (e.key === 'Escape') {
                    document.querySelectorAll('.response-form').forEach(form => {
                        form.style.display = 'none';
                    });
                }
            });
        });

        // Handle form submissions with loading states
        document.addEventListener('submit', function(e) {
            if (e.target.closest('.response-form')) {
                const submitBtn = e.target.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                }
            }
        });
    </script>
</body>
</html>