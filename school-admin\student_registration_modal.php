<?php
// Get all departments for this school
$departments = [];
$stmt = $conn->prepare("SELECT * FROM departments WHERE school_id = ? ORDER BY department_name ASC");
$stmt->bind_param('i', $school_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $departments[] = $row;
}
$stmt->close();

// Get all classes for this school
$classes = [];
$stmt = $conn->prepare("SELECT * FROM classes WHERE school_id = ? ORDER BY grade_level ASC, class_name ASC");
$stmt->bind_param('i', $school_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $classes[] = $row;
}
$stmt->close();
?>

<!-- Student Registration Modal -->
<div id="addStudentMultiStepModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-user-graduate"></i> Add New Student</h2>
            <span class="close-modal" onclick="closeModal('addStudentMultiStepModal')">&times;</span>
        </div>
        <div class="modal-body">
            <?php if (isset($_SESSION['student_error'])): ?>
                <div class="form-alert form-alert-danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $_SESSION['student_error']; unset($_SESSION['student_error']); ?>
                </div>
            <?php endif; ?>
            
            <!-- Form Steps Indicator -->
            <div class="form-steps">
                <div class="step active" id="modal-step1-indicator">
                    <div class="step-number">1</div>
                    <div class="step-title">Student Information</div>
                </div>
                <div class="step" id="modal-step2-indicator">
                    <div class="step-number">2</div>
                    <div class="step-title">Academic Information</div>
                </div>
                <div class="step" id="modal-step3-indicator">
                    <div class="step-number">3</div>
                    <div class="step-title">Parent/Guardian Information</div>
                </div>
            </div>
            
            <form id="modalStudentForm" action="add_student.php" method="post">
                <!-- Ensure redirect to dashboard after successful registration -->
                <input type="hidden" name="redirect_to" value="dashboard.php">
                
                <!-- Step 1: Student Information -->
                <div class="form-section active" id="modal-step1">
                    <h3 class="section-title"><i class="fas fa-user"></i> Student Personal Information</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="modal_first_name" class="required-label">First Name</label>
                            <input type="text" id="modal_first_name" name="first_name" class="form-control" placeholder="Enter first name" required>
                            <div class="error-message" id="modal_first_name-error">Please enter the first name</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_last_name" class="required-label">Last Name</label>
                            <input type="text" id="modal_last_name" name="last_name" class="form-control" placeholder="Enter last name" required>
                            <div class="error-message" id="modal_last_name-error">Please enter the last name</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_gender" class="required-label">Gender</label>
                            <select id="modal_gender" name="gender" class="form-control" required>
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                                <option value="Other">Other</option>
                            </select>
                            <div class="error-message" id="modal_gender-error">Please select a gender</div>
                        </div>

                        <div class="form-group">
                            <label for="modal_dob" class="required-label">Date of Birth</label>
                            <input type="date" id="modal_dob" name="dob" class="form-control" required>
                            <div class="error-message" id="modal_dob-error">Date of birth is required</div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addStudentMultiStepModal')">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="button" class="btn btn-primary" id="modalStep1Next">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Step 2: Academic Information -->
                <div class="form-section" id="modal-step2">
                    <h3 class="section-title"><i class="fas fa-graduation-cap"></i> Academic Information</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="modal_class_id" class="required-label">Class</label>
                            <select id="modal_class_id" name="class_id" class="form-control" required>
                                <option value="">Select Class</option>
                                <?php foreach ($classes as $class): ?>
                                    <option value="<?php echo $class['id']; ?>">
                                        <?php echo htmlspecialchars($class['grade_level'] . ' - ' . $class['class_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="error-message" id="modal_class_id-error">Please select a class</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_department_id">Department</label>
                            <select id="modal_department_id" name="department_id" class="form-control">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $department): ?>
                                    <option value="<?php echo $department['dep_id']; ?>"><?php echo htmlspecialchars($department['department_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <div class="error-message" id="modal_department_id-error">Please select a department</div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="modalStep2Prev">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="modalStep2Next">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Step 3: Parent/Guardian Information -->
                <div class="form-section" id="modal-step3">
                    <h3 class="section-title"><i class="fas fa-users"></i> Parent/Guardian Information</h3>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="modal_parent_name" class="required-label">Parent/Guardian Name</label>
                            <input type="text" id="modal_parent_name" name="parent_name" class="form-control" placeholder="Enter parent/guardian name" required>
                            <div class="error-message" id="modal_parent_name-error">Please enter the parent/guardian name</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_parent_phone" class="required-label">Parent/Guardian Phone</label>
                            <input type="tel" id="modal_parent_phone" name="parent_phone" class="form-control" placeholder="Enter parent/guardian phone" required>
                            <div class="error-message" id="modal_parent_phone-error">Please enter the parent/guardian phone</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_parent_email">Parent/Guardian Email</label>
                            <input type="email" id="modal_parent_email" name="parent_email" class="form-control" placeholder="Enter parent/guardian email">
                            <div class="error-message" id="modal_parent_email-error">Please enter a valid email address</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="modal_address">Address</label>
                            <textarea id="modal_address" name="address" class="form-control" rows="3" placeholder="Enter address"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" id="modalStep3Prev">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                        <button type="button" class="btn btn-primary" id="modalSubmitBtn">
                            <i class="fas fa-save"></i> Save Student
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Confirmation Dialog Removed -->
        </div>
    </div>
</div>

<!-- The multi-step form functionality is now handled by multi-step-form.js -->