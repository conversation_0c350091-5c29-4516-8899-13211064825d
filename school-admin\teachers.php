<?php
// Start session
session_start();

// Show all PHP errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load config
require_once '../config/config.php';

// Check if school admin is logged in
if (!isset($_SESSION['school_admin_id'])) {
    header('Location: ../login.php');
    exit;
}

// Get school_id from session
$school_id = $_SESSION['school_admin_school_id'] ?? 0;
if (!$school_id) {
    die("Error: School ID not found in session. Please log in again.");
}

// Get database connection
$conn = getDbConnection();

// Check if teachers table exists
$result = $conn->query("SHOW TABLES LIKE 'teachers'");
if ($result->num_rows == 0) {
    // Create teachers table if it doesn't exist
    $conn->query("CREATE TABLE IF NOT EXISTS teachers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        school_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        phone VARCHAR(20),
        subject VARCHAR(100),
        qualification VARCHAR(255),
        department_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(dep_id) ON DELETE SET NULL
    )");
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $teacher_id = intval($_GET['id']);
    
    // Delete the teacher
    $stmt = $conn->prepare("DELETE FROM teachers WHERE id = ? AND school_id = ?");
    $stmt->bind_param('ii', $teacher_id, $school_id);
    
    if ($stmt->execute()) {
        $_SESSION['teacher_success'] = 'Teacher deleted successfully!';
    } else {
        $_SESSION['teacher_error'] = 'Failed to delete teacher: ' . $conn->error;
    }
    
    $stmt->close();
    header('Location: teachers.php');
    exit;
}

// Get all teachers for this school with department information
$teachers = [];
try {
    // First check if department_id column exists in teachers table
    $result = $conn->query("SHOW COLUMNS FROM teachers LIKE 'department_id'");
    
    if ($result->num_rows == 0) {
        // Add department_id column if it doesn't exist
        $conn->query("ALTER TABLE teachers ADD COLUMN department_id INT NULL, ADD FOREIGN KEY (department_id) REFERENCES departments(dep_id) ON DELETE SET NULL");
        echo "<div class='alert alert-success'>The teachers table has been updated with department support.</div>";
    }
    
    // Now we can safely use department_id in the join
    $stmt = $conn->prepare("SELECT t.*, d.department_name 
                      FROM teachers t 
                      LEFT JOIN departments d ON t.department_id = d.dep_id 
                      WHERE t.school_id = ? 
                      ORDER BY t.name ASC");
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $teachers[] = $row;
    }
    $stmt->close();
} catch (Exception $e) {
    error_log("Error fetching teachers: " . $e->getMessage());
}

// Get all departments for this school
$departments = [];
$stmt = $conn->prepare("SELECT * FROM departments WHERE school_id = ? ORDER BY department_name ASC");
$stmt->bind_param('i', $school_id);
$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $departments[] = $row;
}
$stmt->close();

// Get school info
$school_info = [];
try {
    $stmt = $conn->prepare('SELECT name, logo, address, phone, email FROM schools WHERE id = ?');
    $stmt->bind_param('i', $school_id);
    $stmt->execute();
    $school_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
} catch (Exception $e) {
    error_log("Error fetching school info: " . $e->getMessage());
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Teachers - <?php echo htmlspecialchars($school_info['name'] ?? 'School'); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/universal-confirmation.css">
    <style>
        :root {
            --primary-color: <?php echo PRIMARY_COLOR ?? '#00704a'; ?>;
            --footer-color: <?php echo FOOTER_COLOR ?? '#f8c301'; ?>;
            --accent-color: <?php echo ACCENT_COLOR ?? '#00704a'; ?>;
            --light-color: #ffffff;
            --dark-color: #333333;
            --gray-color: #f5f5f5;
            --border-color: #e0e0e0;
            --danger-color: #f44336;
            --sidebar-width: 250px;
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --radius-sm: 4px;
            --radius-md: 8px;
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .search-box {
            display: flex;
            align-items: center;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            padding: 0.5rem 1rem;
            box-shadow: var(--shadow-sm);
        }
        
        .search-icon {
            color: var(--primary-color);
            margin-right: 0.5rem;
        }
        
        .search-input {
            flex: 1;
            border: none;
            outline: none;
            font-size: 1rem;
            padding: 0.25rem 0;
        }
        
        .search-clear {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .search-clear:hover {
            color: var(--danger-color);
        }
        
        .empty-search-results {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .empty-search-results i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #ccc;
        }
        
        
        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: var(--gray-color);
            min-height: 100vh;
            display: flex;
        }
        
        /* Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--primary-color);
            color: var(--light-color);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            transition: all 0.3s;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--light-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .school-logo-container {
            display: flex;
            justify-content: center;
            margin-bottom: 1rem;
        }
        
        .school-logo, .school-logo-placeholder {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .school-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .school-logo-placeholder i {
            font-size: 2rem;
            color: var(--primary-color);
        }
        
        .sidebar-logo span {
            color: var(--footer-color);
        }
        
        .sidebar-user {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--accent-color);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
            color: white;
            font-weight: bold;
        }
        
        .user-info h3 {
            font-size: 0.9rem;
            margin-bottom: 0.2rem;
        }
        
        .user-info p {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .menu-heading {
            padding: 0.5rem 1.5rem;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.6;
        }
        
        .menu-item {
            padding: 0.8rem 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s;
        }
        
        .menu-item:hover, .menu-item.active {
            background-color: var(--accent-color);
        }
        
        .menu-item i {
            margin-right: 0.8rem;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }
        
        .menu-item a {
            color: var(--light-color);
            text-decoration: none;
            font-weight: 500;
            flex: 1;
        }
        
        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 2rem;
        }
        
        .page-header {
            margin-bottom: 2rem;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
        }
        
        .breadcrumb a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .breadcrumb span {
            margin: 0 0.5rem;
            color: #999;
        }
        
        /* Card Styles */
        .card {
            background-color: var(--light-color);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.2rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h2 {
            font-size: 1.2rem;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 112, 74, 0.1);
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            border: none;
            font-size: 1rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--accent-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        
        /* Alert Styles */
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid transparent;
        }
        
        .alert-success {
            background-color: #e8f5e9;
            border-color: #4caf50;
            color: #2e7d32;
        }
        
        .alert-danger {
            background-color: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }
        
        /* Table Styles */
        .table-responsive {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }
        
        .data-table th {
            font-weight: 600;
            color: var(--primary-color);
            background-color: rgba(0, 112, 74, 0.05);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
        
        .action-btns {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-icon {
            width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            text-decoration: none;
        }
        
        .btn-icon.edit {
            background-color: var(--primary-color);
        }
        
        .btn-icon.delete {
            background-color: var(--danger-color);
        }
        
        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem;
        }
        
        .empty-icon {
            font-size: 4rem;
            color: #ccc;
            margin-bottom: 1rem;
        }
        
        .empty-text {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        /* Responsive Styles */
        @media (max-width: 992px) {
            .sidebar {
                width: 70px;
                overflow: visible;
            }
            
            .sidebar-header, .sidebar-user, .menu-heading {
                display: none;
            }
            
            .menu-item {
                padding: 1rem 0;
                justify-content: center;
            }
            
            .menu-item i {
                margin-right: 0;
                font-size: 1.3rem;
            }
            
            .menu-item a span {
                display: none;
            }
            
            .main-content {
                margin-left: 70px;
            }
        }
        
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="main-content">
        
        <!-- Page Header -->
        <div class="page-header">
            <h1><i class="fas fa-chalkboard-teacher"></i> Manage Teachers</h1>
            <div class="breadcrumb">
                <a href="dashboard.php">Home</a>
                <span>/</span>
                <span>Teachers</span>
            </div>
        </div>
        
        <!-- Alert Messages -->
        <?php if (isset($_SESSION['teacher_success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php 
                echo $_SESSION['teacher_success']; 
                unset($_SESSION['teacher_success']);
                ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['teacher_error'])): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php 
                echo $_SESSION['teacher_error']; 
                unset($_SESSION['teacher_error']);
                ?>
            </div>
        <?php endif; ?>
        
        <!-- Add Teacher Card -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-user-plus"></i> Add New Teacher</h2>
            </div>
            <div class="card-body">
                <form action="add_teacher.php" method="post">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="teacher_name">Full Name*</label>
                            <input type="text" id="teacher_name" name="teacher_name" class="form-control" placeholder="Enter teacher's full name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="teacher_email">Email Address*</label>
                            <input type="email" id="teacher_email" name="teacher_email" class="form-control" placeholder="Enter email address" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="teacher_phone">Phone Number*</label>
                            <input type="tel" id="teacher_phone" name="teacher_phone" class="form-control" placeholder="Enter phone number" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="teacher_subject">Subject*</label>
                            <input type="text" id="teacher_subject" name="teacher_subject" class="form-control" placeholder="Enter subject taught" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="department_id">Department</label>
                            <select id="department_id" name="department_id" class="form-control">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $department): ?>
                                    <option value="<?php echo $department['dep_id']; ?>">
                                        <?php echo htmlspecialchars($department['department_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="teacher_qualification">Qualification</label>
                            <input type="text" id="teacher_qualification" name="teacher_qualification" class="form-control" placeholder="Enter qualification">
                        </div>
                    </div>
                    
                    <div style="margin-top: 1.5rem;">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus-circle"></i> Add Teacher
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Teachers List Card -->
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-list"></i> All Teachers</h2>
                <!-- Action buttons positioned at the right -->
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <button onclick="exportTeacherData()" class="btn export-btn" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.25rem; font-size: 0.9rem; border-radius: 6px; background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; transition: all 0.3s ease;">
                        <i class="fas fa-download"></i> Export List
                    </button>
                    <button class="btn btn-primary" onclick="openModal('addTeacherModal')" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.75rem 1.5rem; font-size: 0.9rem; border-radius: 6px; transition: all 0.3s ease;">
                        <i class="fas fa-user-plus"></i> Add Teacher
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (count($teachers) > 0): ?>
                    <!-- Search Box -->
                    <div class="search-container" data-table="teachers-table">
                        <div class="search-box">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="Search teachers...">
                            <button type="button" class="search-clear" onclick="clearSearch('teachers-table')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <br>
                    <div class="table-responsive">
                        <table class="data-table" id="teachers-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Subject</th>
                                    <th>Department</th>
                                    <th>Qualification</th>
                                    <th>Date Added</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($teachers as $teacher): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['email']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['phone']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['department_name'] ?? 'Not assigned'); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['qualification'] ?? 'N/A'); ?></td>
                                        <td><?php echo date('M d, Y', strtotime($teacher['created_at'])); ?></td>
                                        <td>
                                            <div class="action-btns">
                                                <a href="#" class="btn-icon edit" data-id="<?php echo $teacher['id']; ?>" title="Edit"><i class="fas fa-edit"></i></a>
                                                <a href="#" class="btn-icon delete" data-id="<?php echo $teacher['id']; ?>" title="Delete"><i class="fas fa-trash"></i></a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <!-- Empty search results message -->
                        <div id="teachers-table-empty-search" class="empty-search-results" style="display: none;">
                            <i class="fas fa-search"></i>
                            <p>No teachers found matching your search.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-user-slash"></i></div>
                        <div class="empty-text">No teachers found</div>
                        <p>Start by adding a new teacher using the form above.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Teacher Edit Modal -->
    <div id="teacherEditModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Edit Teacher</h2>
                <span class="close" onclick="closeTeacherEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="teacherEditFormContainer"></div>
            </div>
        </div>
    </div>
    
    <!-- Confirmation Modal -->
    <div id="confirmationModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h2><i class="fas fa-question-circle"></i> Confirmation</h2>
                <span class="close" onclick="closeConfirmationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p id="confirmationMessage">Are you sure you want to perform this action?</p>
                <div class="form-actions" style="margin-top: 1.5rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeConfirmationModal()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmButton">
                        <i class="fas fa-check"></i> Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <style>
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            width: 80%;
            max-width: 800px;
            animation: modalFadeIn 0.3s;
        }
        
        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }
        
        .modal-header {
            padding: 1rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h2 {
            font-size: 1.2rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .modal-body {
            padding: 1.5rem;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .close {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #f8c301;
        }
        
        .loading-spinner {
            text-align: center;
            padding: 2rem;
            color: var(--primary-color);
            font-size: 1.2rem;
        }
        
        .loading-spinner i {
            margin-right: 0.5rem;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .modal-form .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .section-title {
            font-size: 1.1rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>
    
    <!-- Include search.js for search functionality -->
    <script src="js/search.js"></script>
    
    <script>
        // Global variables
        let currentTeacherId = null;
        let confirmCallback = null;
        
        // Open teacher edit form
        function openTeacherEditForm(teacherId) {
            currentTeacherId = teacherId;
            const modal = document.getElementById('teacherEditModal');
            const formContainer = document.getElementById('teacherEditFormContainer');
            
            // Show loading spinner
            formContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
            modal.style.display = 'block';
            
            // Fetch teacher data
            fetch(`get_edit_form.php?type=teacher&id=${teacherId}`)
                .then(response => response.text())
                .then(html => {
                    formContainer.innerHTML = html;
                    
                    // Add event listener to the form
                    const form = document.getElementById('editForm');
                    if (form) {
                        form.addEventListener('submit', function(e) {
                            e.preventDefault();
                            confirmUpdateTeacher(new FormData(form));
                        });
                    }
                })
                .catch(error => {
                    formContainer.innerHTML = `<div class="alert alert-danger">Error loading form: ${error.message}</div>`;
                });
        }
        
        // Close teacher edit modal
        function closeTeacherEditModal() {
            document.getElementById('teacherEditModal').style.display = 'none';
            currentTeacherId = null;
        }
        
        // Confirm teacher update
        function confirmUpdateTeacher(formData) {
            const modal = document.getElementById('confirmationModal');
            const message = document.getElementById('confirmationMessage');
            const confirmBtn = document.getElementById('confirmButton');
            
            message.innerHTML = 'Are you sure you want to update this teacher information?';
            modal.style.display = 'block';
            
            // Set up the confirm button action
            confirmCallback = function() {
                updateTeacher(formData);
            };
            
            confirmBtn.onclick = function() {
                closeConfirmationModal();
                if (confirmCallback) confirmCallback();
            };
        }
        
        // Update teacher
        function updateTeacher(formData) {
            if (!currentTeacherId) return;
            
            // Add teacher ID to form data
            formData.append('id', currentTeacherId);
            
            // Send update request
            fetch('edit_teacher.php?id=' + currentTeacherId, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Close the edit modal
                closeTeacherEditModal();
                
                // Reload the page to show updated data
                window.location.reload();
            })
            .catch(error => {
                alert('Error updating teacher: ' + error.message);
            });
        }
        
        // Confirm delete teacher
        function confirmDeleteTeacher(teacherId) {
            currentTeacherId = teacherId;
            const modal = document.getElementById('confirmationModal');
            const message = document.getElementById('confirmationMessage');
            const confirmBtn = document.getElementById('confirmButton');
            
            message.innerHTML = 'Are you sure you want to delete this teacher? This action cannot be undone.';
            modal.style.display = 'block';
            
            // Set up the confirm button action
            confirmCallback = function() {
                window.location.href = `teachers.php?action=delete&id=${teacherId}`;
            };
            
            confirmBtn.onclick = function() {
                closeConfirmationModal();
                if (confirmCallback) confirmCallback();
            };
        }
        
        // Close confirmation modal
        function closeConfirmationModal() {
            document.getElementById('confirmationModal').style.display = 'none';
            confirmCallback = null;
        }
        
        // Initialize edit buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to edit buttons
            const editButtons = document.querySelectorAll('a.btn-icon.edit');
            editButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const teacherId = this.getAttribute('data-id');
                    openTeacherEditForm(teacherId);
                });
            });
            
            // Add event listeners to delete buttons
            const deleteButtons = document.querySelectorAll('a.btn-icon.delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const teacherId = this.getAttribute('data-id');
                    confirmDeleteTeacher(teacherId);
                });
            });
        });

        // Export teacher data function
        function exportTeacherData() {
            const table = document.getElementById('teachers-table');
            const rows = table.querySelectorAll('tbody tr:not([style*="display: none"])');

            if (rows.length === 0) {
                alert('No teachers to export');
                return;
            }

            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Name,Email,Phone,Subject,Department,Qualification,Date Added\n";

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0].textContent.trim(),
                    cells[1].textContent.trim(),
                    cells[2].textContent.trim(),
                    cells[3].textContent.trim(),
                    cells[4].textContent.trim(),
                    cells[5].textContent.trim(),
                    cells[6].textContent.trim()
                ];
                csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', 'teachers_list.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Function to open modal
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        }

        // Function to close modal
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }
        }
    </script>

    <style>
        /* Enhanced button hover effects */
        .enhanced-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 112, 74, 0.4);
        }

        .enhanced-btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .enhanced-input:focus, .enhanced-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(0, 112, 74, 0.1);
            transform: translateY(-1px);
        }

        .enhanced-input:focus + .input-icon,
        .enhanced-select:focus + .select-icon {
            color: var(--primary-color);
        }

        .close-modal:hover {
            color: var(--footer-color) !important;
        }
    </style>

    <!-- Add Teacher Modal from Dashboard -->
    <?php
    // Get all departments for dropdown
    $departments = [];
    $conn = getDbConnection();
    try {
        $dept_stmt = $conn->prepare("SELECT dep_id, department_name FROM departments WHERE school_id = ? ORDER BY department_name ASC");
        $dept_stmt->bind_param('i', $school_id);
        $dept_stmt->execute();
        $dept_result = $dept_stmt->get_result();
        while ($row = $dept_result->fetch_assoc()) {
            $departments[] = $row;
        }
        $dept_stmt->close();
    } catch (Exception $e) {
        error_log("Error fetching departments: " . $e->getMessage());
    }
    $conn->close();
    ?>

    <div id="addTeacherModal" class="modal">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); color: white; padding: 1.5rem 2rem; border-radius: 16px 16px 0 0;">
                <h2><i class="fas fa-chalkboard-teacher"></i> Add New Teacher</h2>
                <span class="close-modal" onclick="closeModal('addTeacherModal')" style="color: white; font-size: 1.5rem; cursor: pointer; transition: color 0.3s ease;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 2rem; background-color: #f8f9fa;">
                <?php if (isset($_SESSION['teacher_error'])): ?>
                    <div class="alert alert-danger" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52); color: white; padding: 1rem 1.5rem; border-radius: 12px; margin-bottom: 2rem;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $_SESSION['teacher_error']; unset($_SESSION['teacher_error']); ?>
                    </div>
                <?php endif; ?>

                <form action="add_teacher.php" method="POST" class="modal-form" id="teacherForm">
                    <div class="form-section" style="background-color: white; border-radius: 16px; padding: 2rem; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);">
                        <div class="section-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #f8f9fa;">
                            <div class="section-icon" style="width: 50px; height: 50px; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.3rem;">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div class="section-info">
                                <h3 style="margin: 0; font-size: 1.4rem; font-weight: 600; color: var(--dark-color);">Teacher Information</h3>
                                <p class="section-description" style="margin: 0.25rem 0 0 0; color: #6c757d; font-size: 0.95rem;">Enter the teacher's personal and professional details</p>
                            </div>
                        </div>
                        <div class="form-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-user" style="color: var(--primary-color);"></i>
                                    Full Name <span class="required-asterisk" style="color: #dc3545; font-weight: 700;">*</span>
                                </label>
                                <div class="input-wrapper" style="position: relative;">
                                    <input type="text" id="teacher_name" name="teacher_name" class="enhanced-input" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" placeholder="Enter full name" required>
                                    <i class="fas fa-user input-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-envelope" style="color: var(--primary-color);"></i>
                                    Email <span class="required-asterisk" style="color: #dc3545; font-weight: 700;">*</span>
                                </label>
                                <div class="input-wrapper" style="position: relative;">
                                    <input type="email" id="teacher_email" name="teacher_email" class="enhanced-input" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" placeholder="Enter email address" required>
                                    <i class="fas fa-envelope input-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-phone" style="color: var(--primary-color);"></i>
                                    Phone <span class="required-asterisk" style="color: #dc3545; font-weight: 700;">*</span>
                                </label>
                                <div class="input-wrapper" style="position: relative;">
                                    <input type="tel" id="teacher_phone" name="teacher_phone" class="enhanced-input" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" placeholder="Enter phone number" required>
                                    <i class="fas fa-phone input-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-book" style="color: var(--primary-color);"></i>
                                    Subject <span class="optional-text" style="color: #6c757d; font-weight: 400; font-size: 0.85rem;">(Optional)</span>
                                </label>
                                <div class="input-wrapper" style="position: relative;">
                                    <input type="text" id="teacher_subject" name="teacher_subject" class="enhanced-input" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" placeholder="Enter subject">
                                    <i class="fas fa-book input-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-graduation-cap" style="color: var(--primary-color);"></i>
                                    Qualification <span class="optional-text" style="color: #6c757d; font-weight: 400; font-size: 0.85rem;">(Optional)</span>
                                </label>
                                <div class="input-wrapper" style="position: relative;">
                                    <input type="text" id="teacher_qualification" name="teacher_qualification" class="enhanced-input" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" placeholder="Enter qualification">
                                    <i class="fas fa-graduation-cap input-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="enhanced-label" style="display: flex; align-items: center; gap: 0.5rem; font-weight: 600; color: var(--dark-color); margin-bottom: 0.75rem; font-size: 0.95rem;">
                                    <i class="fas fa-building" style="color: var(--primary-color);"></i>
                                    Department <span class="required-asterisk" style="color: #dc3545; font-weight: 700;">*</span>
                                </label>
                                <div class="select-wrapper" style="position: relative;">
                                    <select id="teacher_department_id" name="department_id" class="enhanced-select" style="width: 100%; padding: 0.875rem 1rem 0.875rem 3rem; border: 2px solid #e9ecef; border-radius: 12px; font-size: 0.95rem; transition: all 0.3s ease;" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['dep_id']; ?>">
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <i class="fas fa-building select-icon" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: #6c757d; pointer-events: none;"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions" style="display: flex; justify-content: space-between; align-items: center; margin-top: 2.5rem; padding-top: 2rem; border-top: 2px solid #f8f9fa;">
                        <button type="submit" class="enhanced-btn-primary" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 2rem; border: none; border-radius: 12px; font-weight: 600; font-size: 0.95rem; cursor: pointer; background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); color: white; box-shadow: 0 4px 15px rgba(0, 112, 74, 0.3); transition: all 0.3s ease;">
                            <i class="fas fa-save"></i> Save Teacher
                        </button>
                        <button type="button" class="enhanced-btn-secondary" onclick="closeModal('addTeacherModal')" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 2rem; border: none; border-radius: 12px; font-weight: 600; font-size: 0.95rem; cursor: pointer; background-color: #6c757d; color: white; box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3); transition: all 0.3s ease;">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/universal-confirmation.js"></script>
</body>
</html>