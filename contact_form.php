<?php
// Initialize the application
require_once 'config/config.php';

// Process school search if submitted
$searchResults = [];
$searchError = '';
$searchQuery = '';

if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchQuery = sanitize($_GET['search']);
    $searchResults = findSchoolByName($searchQuery);
    
    if (!$searchResults) {
        $searchError = 'School not available. Please try another search term.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Smart Communication System for Secondary Schools</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Contact Form Styles */
        .contact-form-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(10, 10, 10, 0.1);
            text-align: center;
        }
        
        .contact-form-section h2 {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .contact-form-section p {
            color: #555;
            margin-bottom: 2rem;
        }
        
        .contact-form {
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            flex: 1;
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .submit-btn {
            background-color: var(--primary-color);
            color: var(--light-color);
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .submit-btn:hover {
            background-color: var(--accent-color);
        }
        
        .form-success {
            background-color: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .form-success i {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
         .copyright {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(10, 10, 10, 0.1);
            color: #555;
        }
        
    </style>
</head>
<body>
            
            <!-- Contact Form Section -->
            <div class="contact-form-section">
                <h2>Send Us a Message</h2>
                <p>Have questions or feedback? We'd love to hear from you!</p>
                
                <?php if (isset($_GET['contact']) && $_GET['contact'] == 'success'): ?>
                    <div class="form-success">
                        <p><i class="fas fa-check-circle"></i> Thank you for your message! We'll get back to you soon.</p>
                    </div>
                <?php endif; ?>
                
                <form action="process_contact.php" method="POST" class="contact-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Your Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Your Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>
                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="submit-btn">Send Message</button>
                </form>
            </div>
            
            <div class="copyright">
                <p>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All Rights Reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>