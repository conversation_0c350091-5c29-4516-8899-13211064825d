:root {
    --form-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    --input-focus-shadow: 0 0 0 3px rgba(25, 118, 210, 0.25);
    --form-radius: 12px;
    --input-radius: 6px;
    --transition-speed: 0.3s;
    --label-color: #455a64;
    --placeholder-color: #90a4ae;
    --success-light: #e8f5e9;
    --danger-light: #ffebee;
    --form-bg: #ffffff;
    --form-border: #f0f0f0;
    --form-padding: 2.5rem;
    --form-gap: 1.8rem;
    --input-padding: 0.85rem 1rem;
    --btn-padding: 0.85rem 1.5rem;
}

/* Form Container Styles */
.form-container {
    background-color: var(--form-bg);
    border-radius: var(--form-radius);
    box-shadow: var(--form-shadow);
    padding: var(--form-padding);
    margin-bottom: 2.5rem;
    border: 1px solid var(--form-border);
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
}

.form-title {
    font-size: 1.6rem;
    margin-bottom: 1.8rem;
    color: var(--primary-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.form-title i {
    font-size: 1.4rem;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.form-subtitle {
    color: var(--gray-dark);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
}

/* Form Grid Layout */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--form-gap);
}

/* Form Group Styles */
.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.6rem;
    font-weight: 500;
    color: var(--label-color);
    font-size: 0.95rem;
}

.required-label::after {
    content: '*';
    color: var(--danger-color);
    margin-left: 4px;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: var(--input-padding);
    border: 1px solid var(--border-color);
    border-radius: var(--input-radius);
    font-size: 1rem;
    transition: all var(--transition-speed) ease;
    background-color: #fafafa;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: var(--input-focus-shadow);
    background-color: #ffffff;
}

.form-control::placeholder {
    color: var(--placeholder-color);
    opacity: 0.7;
}

/* Select Styling */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23455a64' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
    padding-right: 2.5rem;
}

/* Textarea Styling */
textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: var(--btn-padding);
    border-radius: var(--input-radius);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    border: none;
    font-size: 1rem;
    text-decoration: none;
}

.btn i {
    font-size: 1.1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #455a64;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Form Validation Styles */
.form-control.is-invalid {
    border-color: var(--danger-color);
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f44336' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1em;
    padding-right: 2.5rem;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.4rem;
    font-size: 0.85rem;
    color: var(--danger-color);
}

/* Form Alerts */
.form-alert {
    padding: 1rem 1.25rem;
    border-radius: var(--input-radius);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.form-alert i {
    font-size: 1.2rem;
}

.form-alert-success {
    background-color: var(--success-light);
    color: #2e7d32;
    border-left: 4px solid #2e7d32;
}

.form-alert-danger {
    background-color: var(--danger-light);
    color: #c62828;
    border-left: 4px solid #c62828;
}

/* Form Sections */
.form-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--form-border);
}

.form-section-title {
    font-size: 1.2rem;
    color: var(--primary-dark);
    margin-bottom: 1.2rem;
    font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .form-container {
        padding: 1.5rem;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

/* Input Group */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group .form-control {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-append {
    display: flex;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f5f5f5;
    border: 1px solid var(--border-color);
    border-left: none;
    border-top-right-radius: var(--input-radius);
    border-bottom-right-radius: var(--input-radius);
}

/* Form Help Text */
.form-text {
    margin-top: 0.4rem;
    font-size: 0.85rem;
    color: var(--gray-dark);
}

/* Form Checkbox and Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.8rem;
}

.form-check-input {
    margin-right: 0.5rem;
    width: 18px;
    height: 18px;
}

.form-check-label {
    margin-bottom: 0;
    font-weight: normal;
}